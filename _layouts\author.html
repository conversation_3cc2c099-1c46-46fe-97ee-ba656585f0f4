---
layout: page
---

<div class="staff">
  {% if page.photo %}
    <img class="img-rounded" src="{{ page.photo }}" alt="{{ author.display_name }}">
  {% else %}
    <img class="img-rounded" src="/assets/img/user.jpg" alt="{{ author.display_name }}">
  {% endif %}

  <h1 class="name">{{ page.display_name }}</h1>

  {% if page.position %}
    <h2 class="position">{{ page.position }}</h2>
  {% endif %}

  <p>{{ page.bio }}</p>

  <h2>Posts</h2>

  <ul>
    {% assign filtered_posts = site.posts | where: 'author', page.name | where_exp:"post","post.is_generated != true" %}
    {% for post in filtered_posts %}
      <li>
        <a href="{{ site.baseurl }}{{ post.url }}">{{ post.title }}</a>
      </li>
    {% endfor %}
  </ul>
</div>

{% assign author_urls = '' %}
{% if page.github_username %}
    {% assign author_urls = author_urls | append: '"https://github.com/' | append: page.github_username | append: '",' %}
{% endif %}
{% if page.facebook_username %}
    {% assign author_urls = author_urls | append: '"https://www.facebook.com/' | append: page.facebook_username | append: '",' %}
{% endif %}
{% if page.twitter_username %}
    {% assign author_urls = author_urls | append: '"https://twitter.com/' | append: page.twitter_username | append: '",' %}
{% endif %}
{% if page.medium_username %}
    {% assign author_urls = author_urls | append: '"https://medium.com/@' | append: page.medium_username | append: '",' %}
{% endif %}
{% if page.instagram_username %}
    {% assign author_urls = author_urls | append: '"https://www.instagram.com/' | append: page.instagram_username | append: '",' %}
{% endif %}
{% if page.linkedin_username %}
    {% assign author_urls = author_urls | append: '"https://www.linkedin.com/in/' | append: page.linkedin_username | append: '",' %}
{% endif %}

<script type="application/ld+json">
{
    "@context": "http://schema.org",
    "@type": "Person",
    "name": "{{ page.display_name }}",
    {% if page.photo %}
    "image": "{{ page.photo }}",
    {% else %}
    "image": {{ "/assets/img/user.jpg" | prepend: site.baseurl | prepend: site.url }},
    {% endif %}
    "jobTitle": "{{ page.position }}",
    "url": "{{ page.url | prepend: site.baseurl | prepend: site.url }}",
    "sameAs": [
      {{ author_urls | split: "," | join: "," }}
    ]
}
</script>
