<!-- Pagination links -->
{% if paginator.page_trail %}
    {% assign next_posts = site.posts | where_exp:"post","post.is_generated != true" | where_exp:"post","post.path != page.path" | where_exp:"post","post.date <= page.date" %}
    <div class="pagination pagination-post {{ include.extra_classes }}">
        {% if paginator.is_last != true %}
            <a href="{{ paginator.next_page_path }}">
                {% if include.progressBar %}
                    {% include progress-bar.html %}
                {% endif %}
                <span class="text">{{ site.translations.pagination.next_page | default: "Next Page" }} <svg class="arrow"><use xlink:href="#icon-arrow-right"></use></svg></span>
            </a>
        {% else if next_posts.first %}
            <a href="{{ next_posts.first.url }}">
                {% if include.progressBar %}
                    {% include progress-bar.html finished=true %}
                {% endif %}
                <span class="text">{{ site.translations.pagination.next_post | default: "Next Post" }} <svg class="arrow"><use xlink:href="#icon-arrow-right"></use></svg></span>
            </a>
        {% endif %}
    </div>
{% endif %}
