!function(){for(var e=document.querySelectorAll("h1[id],h2[id],h3[id],h4[id],h5[id],h6[id]"),t=0;t<e.length;t++){var i=document.createElement("img"),r=(i.setAttribute("src","/assets/img/link-symbol.svg"),document.createElement("a"));r.setAttribute("href","#"+e[t].getAttribute("id")),r.classList.add("anchor"),r.appendChild(i),e[t].insertBefore(r,e[t].firstChild)}}();
var Zepto=function(){function u(t){return null==t?String(t):_[I.call(t)]||"object"}function o(t){return"function"==u(t)}function a(t){return null!=t&&t==t.window}function s(t){return null!=t&&t.nodeType==t.DOCUMENT_NODE}function i(t){return"object"==u(t)}function c(t){return i(t)&&!a(t)&&Object.getPrototypeOf(t)==Object.prototype}function l(t){return"number"==typeof t.length}function f(t){return t.replace(/::/g,"/").replace(/([A-Z]+)([A-Z][a-z])/g,"$1_$2").replace(/([a-z\d])([A-Z])/g,"$1_$2").replace(/_/g,"-").toLowerCase()}function n(t){return t in e?e[t]:e[t]=new RegExp("(^|\\s)"+t+"(\\s|$)")}function h(t,e){return"number"!=typeof e||M[f(t)]?e:e+"px"}function r(t){return"children"in t?T.call(t.children):b.map(t.childNodes,function(t){return 1==t.nodeType?t:void 0})}function p(t,e){return null==e?b(t):b(t).filter(e)}function d(t,e,n,r){return o(e)?e.call(t,n,r):e}function m(t,e,n){null==n?t.removeAttribute(e):t.setAttribute(e,n)}function v(t,e){var n=t.className||"",r=n&&n.baseVal!==y;return e===y?r?n.baseVal:n:void(r?n.baseVal=e:t.className=e)}function g(e){try{return e&&("true"==e||"false"!=e&&("null"==e?null:+e+""==e?+e:/^[\[\{]/.test(e)?b.parseJSON(e):e))}catch(t){return e}}var y,x,b,w,E,j,S=[],T=S.slice,C=S.filter,N=window.document,O={},e={},M={"column-count":1,columns:1,"font-weight":1,"line-height":1,opacity:1,"z-index":1,zoom:1},P=/^\s*<(\w+|!)[^>]*>/,R=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,k=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,A=/^(?:body|html)$/i,z=/([A-Z])/g,F=["val","css","html","text","data","width","height","offset"],t=N.createElement("table"),L=N.createElement("tr"),Z={tr:N.createElement("tbody"),tbody:t,thead:t,tfoot:t,td:L,th:L,"*":N.createElement("div")},q=/complete|loaded|interactive/,H=/^[\w-]*$/,_={},I=_.toString,$={},V=N.createElement("div"),B={tabindex:"tabIndex",readonly:"readOnly",for:"htmlFor",class:"className",maxlength:"maxLength",cellspacing:"cellSpacing",cellpadding:"cellPadding",rowspan:"rowSpan",colspan:"colSpan",usemap:"useMap",frameborder:"frameBorder",contenteditable:"contentEditable"},D=Array.isArray||function(t){return t instanceof Array};return $.matches=function(t,e){var n,r;return!(!e||!t||1!==t.nodeType)&&((n=t.webkitMatchesSelector||t.mozMatchesSelector||t.oMatchesSelector||t.matchesSelector)?n.call(t,e):((r=!(n=t.parentNode))&&(n=V).appendChild(t),n=~$.qsa(n,e).indexOf(t),r&&V.removeChild(t),n))},E=function(t){return t.replace(/-+(.)?/g,function(t,e){return e?e.toUpperCase():""})},j=function(n){return C.call(n,function(t,e){return n.indexOf(t)==e})},$.fragment=function(t,e,n){var r,i,o;return(r=R.test(t)?b(N.createElement(RegExp.$1)):r)||(t.replace&&(t=t.replace(k,"<$1></$2>")),e===y&&(e=P.test(t)&&RegExp.$1),(o=Z[e=e in Z?e:"*"]).innerHTML=""+t,r=b.each(T.call(o.childNodes),function(){o.removeChild(this)})),c(n)&&(i=b(r),b.each(n,function(t,e){-1<F.indexOf(t)?i[t](e):i.attr(t,e)})),r},$.Z=function(t,e){return(t=t||[]).__proto__=b.fn,t.selector=e||"",t},$.isZ=function(t){return t instanceof $.Z},$.init=function(t,e){var n,r;if(!t)return $.Z();if("string"==typeof t)if("<"==(t=t.trim())[0]&&P.test(t))n=$.fragment(t,RegExp.$1,e),t=null;else{if(e!==y)return b(e).find(t);n=$.qsa(N,t)}else{if(o(t))return b(N).ready(t);if($.isZ(t))return t;if(D(t))r=t,n=C.call(r,function(t){return null!=t});else if(i(t))n=[t],t=null;else if(P.test(t))n=$.fragment(t.trim(),RegExp.$1,e),t=null;else{if(e!==y)return b(e).find(t);n=$.qsa(N,t)}}return $.Z(n,t)},(b=function(t,e){return $.init(t,e)}).extend=function(e){var n,t=T.call(arguments,1);return"boolean"==typeof e&&(n=e,e=t.shift()),t.forEach(function(t){!function t(e,n,r){for(x in n)r&&(c(n[x])||D(n[x]))?(c(n[x])&&!c(e[x])&&(e[x]={}),D(n[x])&&!D(e[x])&&(e[x]=[]),t(e[x],n[x],r)):n[x]!==y&&(e[x]=n[x])}(e,t,n)}),e},$.qsa=function(t,e){var n,r="#"==e[0],i=!r&&"."==e[0],o=r||i?e.slice(1):e,a=H.test(o);return s(t)&&a&&r?(n=t.getElementById(o))?[n]:[]:1!==t.nodeType&&9!==t.nodeType?[]:T.call(a&&!r?i?t.getElementsByClassName(o):t.getElementsByTagName(e):t.querySelectorAll(e))},b.contains=N.documentElement.contains?function(t,e){return t!==e&&t.contains(e)}:function(t,e){for(;e=e&&e.parentNode;)if(e===t)return!0;return!1},b.type=u,b.isFunction=o,b.isWindow=a,b.isArray=D,b.isPlainObject=c,b.isEmptyObject=function(t){for(var e in t)return!1;return!0},b.inArray=function(t,e,n){return S.indexOf.call(e,t,n)},b.camelCase=E,b.trim=function(t){return null==t?"":String.prototype.trim.call(t)},b.uuid=0,b.support={},b.expr={},b.map=function(t,e){var n,r,i,o,a=[];if(l(t))for(r=0;r<t.length;r++)null!=(n=e(t[r],r))&&a.push(n);else for(i in t)n=e(t[i],i),null!=n&&a.push(n);return 0<(o=a).length?b.fn.concat.apply([],o):o},b.each=function(t,e){var n,r;if(l(t)){for(n=0;n<t.length;n++)if(!1===e.call(t[n],n,t[n]))return t}else for(r in t)if(!1===e.call(t[r],r,t[r]))return t;return t},b.grep=function(t,e){return C.call(t,e)},window.JSON&&(b.parseJSON=JSON.parse),b.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(t,e){_["[object "+e+"]"]=e.toLowerCase()}),b.fn={forEach:S.forEach,reduce:S.reduce,push:S.push,sort:S.sort,indexOf:S.indexOf,concat:S.concat,map:function(n){return b(b.map(this,function(t,e){return n.call(t,e,t)}))},slice:function(){return b(T.apply(this,arguments))},ready:function(t){return q.test(N.readyState)&&N.body?t(b):N.addEventListener("DOMContentLoaded",function(){t(b)},!1),this},get:function(t){return t===y?T.call(this):this[0<=t?t:t+this.length]},toArray:function(){return this.get()},size:function(){return this.length},remove:function(){return this.each(function(){null!=this.parentNode&&this.parentNode.removeChild(this)})},each:function(n){return S.every.call(this,function(t,e){return!1!==n.call(t,e,t)}),this},filter:function(e){return o(e)?this.not(this.not(e)):b(C.call(this,function(t){return $.matches(t,e)}))},add:function(t,e){return b(j(this.concat(b(t,e))))},is:function(t){return 0<this.length&&$.matches(this[0],t)},not:function(e){var n,r=[];return o(e)&&e.call!==y?this.each(function(t){e.call(this,t)||r.push(this)}):(n="string"==typeof e?this.filter(e):l(e)&&o(e.item)?T.call(e):b(e),this.forEach(function(t){n.indexOf(t)<0&&r.push(t)})),b(r)},has:function(t){return this.filter(function(){return i(t)?b.contains(this,t):b(this).find(t).size()})},eq:function(t){return-1===t?this.slice(t):this.slice(t,+t+1)},first:function(){var t=this[0];return t&&!i(t)?t:b(t)},last:function(){var t=this[this.length-1];return t&&!i(t)?t:b(t)},find:function(t){var n=this;return t?"object"==typeof t?b(t).filter(function(){var e=this;return S.some.call(n,function(t){return b.contains(t,e)})}):1==this.length?b($.qsa(this[0],t)):this.map(function(){return $.qsa(this,t)}):b()},closest:function(t,e){var n=this[0],r=!1;for("object"==typeof t&&(r=b(t));n&&!(r?0<=r.indexOf(n):$.matches(n,t));)n=n!==e&&!s(n)&&n.parentNode;return b(n)},parents:function(t){for(var e=[],n=this;0<n.length;)n=b.map(n,function(t){return(t=t.parentNode)&&!s(t)&&e.indexOf(t)<0?(e.push(t),t):void 0});return p(e,t)},parent:function(t){return p(j(this.pluck("parentNode")),t)},children:function(t){return p(this.map(function(){return r(this)}),t)},contents:function(){return this.map(function(){return T.call(this.childNodes)})},siblings:function(t){return p(this.map(function(t,e){return C.call(r(e.parentNode),function(t){return t!==e})}),t)},empty:function(){return this.each(function(){this.innerHTML=""})},pluck:function(e){return b.map(this,function(t){return t[e]})},show:function(){return this.each(function(){var t,e,n;"none"==this.style.display&&(this.style.display=""),"none"==getComputedStyle(this,"").getPropertyValue("display")&&(this.style.display=(t=this.nodeName,O[t]||(e=N.createElement(t),N.body.appendChild(e),n=getComputedStyle(e,"").getPropertyValue("display"),e.parentNode.removeChild(e),O[t]=n="none"==n?"block":n),O[t]))})},replaceWith:function(t){return this.before(t).remove()},wrap:function(e){var n,r,i=o(e);return this[0]&&!i&&(n=b(e).get(0),r=n.parentNode||1<this.length),this.each(function(t){b(this).wrapAll(i?e.call(this,t):r?n.cloneNode(!0):n)})},wrapAll:function(t){if(this[0]){b(this[0]).before(t=b(t));for(var e;(e=t.children()).length;)t=e.first();b(t).append(this)}return this},wrapInner:function(r){var i=o(r);return this.each(function(t){var e=b(this),n=e.contents(),t=i?r.call(this,t):r;n.length?n.wrapAll(t):e.append(t)})},unwrap:function(){return this.parent().each(function(){b(this).replaceWith(b(this).children())}),this},clone:function(){return this.map(function(){return this.cloneNode(!0)})},hide:function(){return this.css("display","none")},toggle:function(e){return this.each(function(){var t=b(this);(e===y?"none"==t.css("display"):e)?t.show():t.hide()})},prev:function(t){return b(this.pluck("previousElementSibling")).filter(t||"*")},next:function(t){return b(this.pluck("nextElementSibling")).filter(t||"*")},html:function(n){return 0 in arguments?this.each(function(t){var e=this.innerHTML;b(this).empty().append(d(this,n,t,e))}):0 in this?this[0].innerHTML:null},text:function(e){return 0 in arguments?this.each(function(t){t=d(this,e,t,this.textContent);this.textContent=null==t?"":""+t}):0 in this?this[0].textContent:null},attr:function(e,n){var t;return"string"!=typeof e||1 in arguments?this.each(function(t){if(1===this.nodeType)if(i(e))for(x in e)m(this,x,e[x]);else m(this,e,d(this,n,t,this.getAttribute(e)))}):this.length&&1===this[0].nodeType?!(t=this[0].getAttribute(e))&&e in this[0]?this[0][e]:t:y},removeAttr:function(t){return this.each(function(){1===this.nodeType&&t.split(" ").forEach(function(t){m(this,t)},this)})},prop:function(e,n){return e=B[e]||e,1 in arguments?this.each(function(t){this[e]=d(this,n,t,this[e])}):this[0]&&this[0][e]},data:function(t,e){var n="data-"+t.replace(z,"-$1").toLowerCase(),n=1 in arguments?this.attr(n,e):this.attr(n);return null!==n?g(n):y},val:function(e){return 0 in arguments?this.each(function(t){this.value=d(this,e,t,this.value)}):this[0]&&(this[0].multiple?b(this[0]).find("option").filter(function(){return this.selected}).pluck("value"):this[0].value)},offset:function(r){var t;return r?this.each(function(t){var e=b(this),t=d(this,r,t,e.offset()),n=e.offsetParent().offset(),t={top:t.top-n.top,left:t.left-n.left};"static"==e.css("position")&&(t.position="relative"),e.css(t)}):this.length?{left:(t=this[0].getBoundingClientRect()).left+window.pageXOffset,top:t.top+window.pageYOffset,width:Math.round(t.width),height:Math.round(t.height)}:null},css:function(t,e){if(arguments.length<2){var n,r,i=this[0];if(!i)return;if(n=getComputedStyle(i,""),"string"==typeof t)return i.style[E(t)]||n.getPropertyValue(t);if(D(t))return r={},b.each(t,function(t,e){r[e]=i.style[E(e)]||n.getPropertyValue(e)}),r}var o="";if("string"==u(t))e||0===e?o=f(t)+":"+h(t,e):this.each(function(){this.style.removeProperty(f(t))});else for(x in t)t[x]||0===t[x]?o+=f(x)+":"+h(x,t[x])+";":this.each(function(){this.style.removeProperty(f(x))});return this.each(function(){this.style.cssText+=";"+o})},index:function(t){return t?this.indexOf(b(t)[0]):this.parent().children().indexOf(this[0])},hasClass:function(t){return!!t&&S.some.call(this,function(t){return this.test(v(t))},n(t))},addClass:function(n){return n?this.each(function(t){var e;"className"in this&&(w=[],e=v(this),d(this,n,t,e).split(/\s+/g).forEach(function(t){b(this).hasClass(t)||w.push(t)},this),w.length&&v(this,e+(e?" ":"")+w.join(" ")))}):this},removeClass:function(e){return this.each(function(t){if("className"in this){if(e===y)return v(this,"");w=v(this),d(this,e,t,w).split(/\s+/g).forEach(function(t){w=w.replace(n(t)," ")}),v(this,w.trim())}})},toggleClass:function(n,r){return n?this.each(function(t){var e=b(this);d(this,n,t,v(this)).split(/\s+/g).forEach(function(t){(r===y?!e.hasClass(t):r)?e.addClass(t):e.removeClass(t)})}):this},scrollTop:function(t){var e;if(this.length)return e="scrollTop"in this[0],t===y?e?this[0].scrollTop:this[0].pageYOffset:this.each(e?function(){this.scrollTop=t}:function(){this.scrollTo(this.scrollX,t)})},scrollLeft:function(t){var e;if(this.length)return e="scrollLeft"in this[0],t===y?e?this[0].scrollLeft:this[0].pageXOffset:this.each(e?function(){this.scrollLeft=t}:function(){this.scrollTo(t,this.scrollY)})},position:function(){var t,e,n,r;if(this.length)return t=this[0],e=this.offsetParent(),n=this.offset(),r=A.test(e[0].nodeName)?{top:0,left:0}:e.offset(),n.top-=parseFloat(b(t).css("margin-top"))||0,n.left-=parseFloat(b(t).css("margin-left"))||0,r.top+=parseFloat(b(e[0]).css("border-top-width"))||0,r.left+=parseFloat(b(e[0]).css("border-left-width"))||0,{top:n.top-r.top,left:n.left-r.left}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent||N.body;t&&!A.test(t.nodeName)&&"static"==b(t).css("position");)t=t.offsetParent;return t})}},b.fn.detach=b.fn.remove,["width","height"].forEach(function(r){var i=r.replace(/./,function(t){return t[0].toUpperCase()});b.fn[r]=function(e){var t,n=this[0];return e===y?a(n)?n["inner"+i]:s(n)?n.documentElement["scroll"+i]:(t=this.offset())&&t[r]:this.each(function(t){(n=b(this)).css(r,d(this,e,t,n[r]()))})}}),["after","prepend","before","append"].forEach(function(e,a){var s=a%2;b.fn[e]=function(){var e,r,i=b.map(arguments,function(t){return"object"==(e=u(t))||"array"==e||null==t?t:$.fragment(t)}),o=1<this.length;return i.length<1?this:this.each(function(t,e){r=s?e:e.parentNode,e=0==a?e.nextSibling:1==a?e.firstChild:2==a?e:null;var n=b.contains(N.documentElement,r);i.forEach(function(t){if(o)t=t.cloneNode(!0);else if(!r)return b(t).remove();r.insertBefore(t,e),n&&function t(e,n){n(e);for(var r=0,i=e.childNodes.length;r<i;r++)t(e.childNodes[r],n)}(t,function(t){null==t.nodeName||"SCRIPT"!==t.nodeName.toUpperCase()||t.type&&"text/javascript"!==t.type||t.src||window.eval.call(window,t.innerHTML)})})})},b.fn[s?e+"To":"insert"+(a?"Before":"After")]=function(t){return b(t)[e](this),this}}),$.Z.prototype=b.fn,$.uniq=j,$.deserializeValue=g,b.zepto=$,b}();window.Zepto=Zepto,void 0===window.$&&(window.$=Zepto),function(l){function f(t){return t._zid||(t._zid=e++)}function a(t,e,n,r){var i,o;return(e=h(e)).ns&&(o=e.ns,i=new RegExp("(?:^| )"+o.replace(" "," .* ?")+"(?: |$)")),(E[f(t)]||[]).filter(function(t){return!(!t||e.e&&t.e!=e.e||e.ns&&!i.test(t.ns)||n&&f(t.fn)!==f(n)||r&&t.sel!=r)})}function h(t){t=(""+t).split(".");return{e:t[0],ns:t.slice(1).sort().join(" ")}}function p(t,e){return t.del&&!n&&t.e in r||!!e}function d(t){return j[t]||n&&r[t]||t}function c(i,t,e,o,a,s,u){var n=f(i),c=E[n]||(E[n]=[]);t.split(/\s/).forEach(function(t){if("ready"==t)return l(document).ready(e);var n=h(t),r=(n.fn=e,n.sel=a,n.e in j&&(e=function(t){var e=t.relatedTarget;return!e||e!==this&&!l.contains(this,e)?n.fn.apply(this,arguments):void 0}),(n.del=s)||e);n.proxy=function(t){var e;if(!(t=v(t)).isImmediatePropagationStopped())return t.data=o,!1===(e=r.apply(i,t._args==x?[t]:[t].concat(t._args)))&&(t.preventDefault(),t.stopPropagation()),e},n.i=c.length,c.push(n),"addEventListener"in i&&i.addEventListener(d(n.e),n.proxy,p(n,u))})}function m(e,t,n,r,i){var o=f(e);(t||"").split(/\s/).forEach(function(t){a(e,t,n,r).forEach(function(t){delete E[o][t.i],"removeEventListener"in e&&e.removeEventListener(d(t.e),t.proxy,p(t,i))})})}function v(r,i){return!i&&r.isDefaultPrevented||(i=i||r,l.each(t,function(t,e){var n=i[t];r[t]=function(){return this[e]=s,n&&n.apply(i,arguments)},r[e]=S}),(i.defaultPrevented!==x?i.defaultPrevented:"returnValue"in i?!1===i.returnValue:i.getPreventDefault&&i.getPreventDefault())&&(r.isDefaultPrevented=s)),r}function g(t){var e,n={originalEvent:t};for(e in t)i.test(e)||t[e]===x||(n[e]=t[e]);return v(n,t)}function y(t){return"string"==typeof t}var x,e=1,b=Array.prototype.slice,w=l.isFunction,E={},o={},n="onfocusin"in window,r={focus:"focusin",blur:"focusout"},j={mouseenter:"mouseover",mouseleave:"mouseout"},s=(o.click=o.mousedown=o.mouseup=o.mousemove="MouseEvents",l.event={add:c,remove:m},l.proxy=function(t,e){var n,r=2 in arguments&&b.call(arguments,2);if(w(t))return(n=function(){return t.apply(e,r?r.concat(b.call(arguments)):arguments)})._zid=f(t),n;if(y(e))return r?(r.unshift(t[e],t),l.proxy.apply(null,r)):l.proxy(t[e],t);throw new TypeError("expected function")},l.fn.bind=function(t,e,n){return this.on(t,e,n)},l.fn.unbind=function(t,e){return this.off(t,e)},l.fn.one=function(t,e,n,r){return this.on(t,e,n,r,1)},function(){return!0}),S=function(){return!1},i=/^([A-Z]|returnValue$|layer[XY]$)/,t={preventDefault:"isDefaultPrevented",stopImmediatePropagation:"isImmediatePropagationStopped",stopPropagation:"isPropagationStopped"};l.fn.delegate=function(t,e,n){return this.on(e,t,n)},l.fn.undelegate=function(t,e,n){return this.off(e,t,n)},l.fn.live=function(t,e){return l(document.body).delegate(this.selector,t,e),this},l.fn.die=function(t,e){return l(document.body).undelegate(this.selector,t,e),this},l.fn.on=function(e,i,n,o,a){var s,u,r=this;return e&&!y(e)?(l.each(e,function(t,e){r.on(t,i,n,e,a)}),r):(y(i)||w(o)||!1===o||(o=n,n=i,i=x),!w(n)&&!1!==n||(o=n,n=x),!1===o&&(o=S),r.each(function(t,r){a&&(s=function(t){return m(r,t.type,o),o.apply(this,arguments)}),c(r,e,o,n,i,(u=i?function(t){var e,n=l(t.target).closest(i,r).get(0);return n&&n!==r?(e=l.extend(g(t),{currentTarget:n,liveFired:r}),(s||o).apply(n,[e].concat(b.call(arguments,1)))):void 0}:u)||s)}))},l.fn.off=function(t,n,e){var r=this;return t&&!y(t)?(l.each(t,function(t,e){r.off(t,n,e)}),r):(y(n)||w(e)||!1===e||(e=n,n=x),!1===e&&(e=S),r.each(function(){m(this,t,e,n)}))},l.fn.trigger=function(t,e){return(t=y(t)||l.isPlainObject(t)?l.Event(t):v(t))._args=e,this.each(function(){t.type in r&&"function"==typeof this[t.type]?this[t.type]():"dispatchEvent"in this?this.dispatchEvent(t):l(this).triggerHandler(t,e)})},l.fn.triggerHandler=function(n,r){var i,o;return this.each(function(t,e){(i=g(y(n)?l.Event(n):n))._args=r,i.target=e,l.each(a(e,n.type||n),function(t,e){return o=e.proxy(i),!i.isImmediatePropagationStopped()&&void 0})}),o},"focusin focusout focus blur load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select keydown keypress keyup error".split(" ").forEach(function(e){l.fn[e]=function(t){return 0 in arguments?this.bind(e,t):this.trigger(e)}}),l.Event=function(t,e){y(t)||(t=(e=t).type);var n=document.createEvent(o[t]||"Events"),r=!0;if(e)for(var i in e)"bubbles"==i?r=!!e[i]:n[i]=e[i];return n.initEvent(t,r,!0),v(n)}}(Zepto),function(f){function h(t,e,n,r){return t.global?(t=e||b,e=n,n=r,e=f.Event(e),f(t).trigger(e,n),!e.isDefaultPrevented()):void 0}function p(t,e){var n=e.context;return!1!==e.beforeSend.call(n,t,e)&&!1!==h(e,n,"ajaxBeforeSend",[t,e])&&void h(e,n,"ajaxSend",[t,e])}function d(t,e,n,r){var i=n.context,o="success";n.success.call(i,t,o,e),r&&r.resolveWith(i,[t,o,e]),h(n,i,"ajaxSuccess",[e,n,t]),a(o,e,n)}function m(t,e,n,r,i){var o=r.context;r.error.call(o,n,e,t),i&&i.rejectWith(o,[n,e,t]),h(r,o,"ajaxError",[n,r,t||e]),a(e,n,r)}function a(t,e,n){var r=n.context;n.complete.call(r,e,t),h(n,r,"ajaxComplete",[e,n]),(t=n).global&&!--f.active&&h(t,null,"ajaxStop")}function v(){}function g(t,e){return""==e?t:(t+"&"+e).replace(/[&?]{1,2}/,"?")}function s(t,e,n,r){return f.isFunction(e)&&(r=n,n=e,e=void 0),f.isFunction(n)||(r=n,n=void 0),{url:t,data:e,success:n,dataType:r}}var y,x,l=0,b=window.document,u=/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,w=/^(?:text|application)\/javascript/i,E=/^(?:text|application)\/xml/i,j="application/json",S="text/html",T=/^\s*$/,C=b.createElement("a"),r=(C.href=window.location.href,f.active=0,f.ajaxJSONP=function(n,r){var i,o,t,a,s,u,e,c;return"type"in n?(t=n.jsonpCallback,a=(f.isFunction(t)?t():t)||"jsonp"+ ++l,s=b.createElement("script"),u=window[a],c={abort:e=function(t){f(s).triggerHandler("error",t||"abort")}},r&&r.promise(c),f(s).on("load error",function(t,e){clearTimeout(o),f(s).off().remove(),"error"!=t.type&&i?d(i[0],c,n,r):m(null,e||"error",c,n,r),window[a]=u,i&&f.isFunction(u)&&u(i[0]),u=i=void 0}),!1===p(c,n)?e("abort"):(window[a]=function(){i=arguments},s.src=n.url.replace(/\?(.+)=\?/,"?$1="+a),b.head.appendChild(s),0<n.timeout&&(o=setTimeout(function(){e("timeout")},n.timeout))),c):f.ajax(n)},f.ajaxSettings={type:"GET",beforeSend:v,success:v,error:v,complete:v,context:null,global:!0,xhr:function(){return new window.XMLHttpRequest},accepts:{script:"text/javascript, application/javascript, application/x-javascript",json:j,xml:"application/xml, text/xml",html:S,text:"text/plain"},crossDomain:!1,timeout:0,processData:!0,cache:!0},f.ajax=function(t){var n=f.extend({},t||{}),r=f.Deferred&&f.Deferred();for(y in f.ajaxSettings)void 0===n[y]&&(n[y]=f.ajaxSettings[y]);(e=n).global&&0==f.active++&&h(e,null,"ajaxStart"),n.crossDomain||((e=b.createElement("a")).href=n.url,e.href=e.href,n.crossDomain=C.protocol+"//"+C.host!=e.protocol+"//"+e.host),n.url||(n.url=window.location.toString()),(e=n).processData&&e.data&&"string"!=f.type(e.data)&&(e.data=f.param(e.data,e.traditional)),!e.data||e.type&&"GET"!=e.type.toUpperCase()||(e.url=g(e.url,e.data),e.data=void 0);var i=n.dataType,e=/\?.+=\?/.test(n.url);if(e&&(i="jsonp"),!1!==n.cache&&(t&&!0===t.cache||"script"!=i&&"jsonp"!=i)||(n.url=g(n.url,"_="+Date.now())),"jsonp"==i)return e||(n.url=g(n.url,n.jsonp?n.jsonp+"=?":!1===n.jsonp?"":"callback=?")),f.ajaxJSONP(n,r);function o(t,e){s[t.toLowerCase()]=[t,e]}var a,t=n.accepts[i],s={},u=/^([\w-]+:)\/\//.test(n.url)?RegExp.$1:window.location.protocol,c=n.xhr(),l=c.setRequestHeader;if(r&&r.promise(c),n.crossDomain||o("X-Requested-With","XMLHttpRequest"),o("Accept",t||"*/*"),(t=n.mimeType||t)&&(-1<t.indexOf(",")&&(t=t.split(",",2)[0]),c.overrideMimeType&&c.overrideMimeType(t)),(n.contentType||!1!==n.contentType&&n.data&&"GET"!=n.type.toUpperCase())&&o("Content-Type",n.contentType||"application/x-www-form-urlencoded"),n.headers)for(x in n.headers)o(x,n.headers[x]);if(c.setRequestHeader=o,!(c.onreadystatechange=function(){if(4==c.readyState){c.onreadystatechange=v,clearTimeout(a);var e=!1;if(200<=c.status&&c.status<300||304==c.status||0==c.status&&"file:"==u){i=i||((t=(t=n.mimeType||c.getResponseHeader("content-type"))&&t.split(";",2)[0])&&(t==S?"html":t==j?"json":w.test(t)?"script":E.test(t)&&"xml")||"text"),t=c.responseText;try{"script"==i?(0,eval)(t):"xml"==i?t=c.responseXML:"json"==i&&(t=T.test(t)?null:f.parseJSON(t))}catch(t){e=t}e?m(e,"parsererror",c,n,r):d(t,c,n,r)}else m(c.statusText||null,c.status?"error":"abort",c,n,r)}var t})===p(c,n))c.abort(),m(null,"abort",c,n,r);else{if(n.xhrFields)for(x in n.xhrFields)c[x]=n.xhrFields[x];e=!("async"in n)||n.async;for(x in c.open(n.type,n.url,e,n.username,n.password),s)l.apply(c,s[x]);0<n.timeout&&(a=setTimeout(function(){c.onreadystatechange=v,c.abort(),m(null,"timeout",c,n,r)},n.timeout)),c.send(n.data||null)}return c},f.get=function(){return f.ajax(s.apply(null,arguments))},f.post=function(){var t=s.apply(null,arguments);return t.type="POST",f.ajax(t)},f.getJSON=function(){var t=s.apply(null,arguments);return t.dataType="json",f.ajax(t)},f.fn.load=function(t,e,n){var r,i,o,a;return this.length&&(i=this,o=t.split(/\s/),t=s(t,e,n),a=t.success,1<o.length&&(t.url=o[0],r=o[1]),t.success=function(t){i.html(r?f("<div>").html(t.replace(u,"")).find(r):t),a&&a.apply(i,arguments)},f.ajax(t)),this},encodeURIComponent);f.param=function(t,e){var n=[];return n.add=function(t,e){null==(e=f.isFunction(e)?e():e)&&(e=""),this.push(r(t)+"="+r(e))},function n(r,t,i,o){var a,s=f.isArray(t),u=f.isPlainObject(t);f.each(t,function(t,e){a=f.type(e),o&&(t=i?o:o+"["+(u||"object"==a||"array"==a?t:"")+"]"),!o&&s?r.add(e.name,e.value):"array"==a||!i&&"object"==a?n(r,e,i,t):r.add(t,e)})}(n,t,e),n.join("&").replace(/%20/g,"+")}}(Zepto),function(o){o.fn.serializeArray=function(){function n(t){return t.forEach?t.forEach(n):void e.push({name:r,value:t})}var r,i,e=[];return this[0]&&o.each(this[0].elements,function(t,e){i=e.type,(r=e.name)&&"fieldset"!=e.nodeName.toLowerCase()&&!e.disabled&&"submit"!=i&&"reset"!=i&&"button"!=i&&"file"!=i&&("radio"!=i&&"checkbox"!=i||e.checked)&&n(o(e).val())}),e},o.fn.serialize=function(){var e=[];return this.serializeArray().forEach(function(t){e.push(encodeURIComponent(t.name)+"="+encodeURIComponent(t.value))}),e.join("&")},o.fn.submit=function(t){var e;return 0 in arguments?this.bind("submit",t):this.length&&(e=o.Event("submit"),this.eq(0).trigger(e),e.isDefaultPrevented()||this.get(0).submit()),this}}(Zepto),function(n){"__proto__"in{}||n.extend(n.zepto,{Z:function(t,e){return n.extend(t=t||[],n.fn),t.selector=e||"",t.__Z=!0,t},isZ:function(t){return"array"===n.type(t)&&"__Z"in t}});try{getComputedStyle(void 0)}catch(t){var e=getComputedStyle;window.getComputedStyle=function(t){try{return e(t)}catch(t){return null}}}}(Zepto);
!function(s){"use strict";function n(s){return new RegExp("(^|\\s+)"+s+"(\\s+|$)")}var a,t;function e(s,e){(a(s,e)?c:t)(s,e)}var c="classList"in document.documentElement?(a=function(s,e){return s.classList.contains(e)},t=function(s,e){s.classList.add(e)},function(s,e){s.classList.remove(e)}):(a=function(s,e){return n(e).test(s.className)},t=function(s,e){a(s,e)||(s.className=s.className+" "+e)},function(s,e){s.className=s.className.replace(n(e)," ")}),i={hasClass:a,addClass:t,removeClass:c,toggleClass:e,has:a,add:t,remove:c,toggle:e};"function"==typeof define&&define.amd?define(i):s.classie=i}(window);
!function(e,o){"function"==typeof define&&define.amd?define(o):"object"==typeof exports?module.exports=o(require,0,module):e.ouibounce=o()}(this,function(e,o,i){return function(e,o){"use strict";var o=o||{},i=o.aggressive||!1,n=l(o.sensitivity,20),t=l(o.timer,1e3),r=l(o.delay,0),u=o.callback||function(){},c=v(o.cookieExpire)||"",a=o.cookieDomain?";domain="+o.cookieDomain:"",d=o.cookieName||"viewedOuibounceModal",s=!0===o.sitewide?";path=/":"",m=null,f=document.documentElement;function l(e,o){return void 0===e?o:e}function v(e){var e=24*e*60*60*1e3,o=new Date;return o.setTime(o.getTime()+e),"; expires="+o.toUTCString()}function k(e){e.clientY>n||(m=setTimeout(w,r))}function p(){m&&(clearTimeout(m),m=null)}setTimeout(function(){T()||(f.addEventListener("mouseleave",k),f.addEventListener("mouseenter",p),f.addEventListener("keydown",E))},t);var y=!1;function E(e){y||e.metaKey&&76===e.keyCode&&(y=!0,m=setTimeout(w,r))}function b(e,o){return function(){for(var e=document.cookie.split("; "),o={},i=e.length-1;0<=i;i--){var n=e[i].split("=");o[n[0]]=n[1]}return o}()[e]===o}function T(){return b(d,"true")&&!i}function w(){T()||(e&&(e.style.display="block"),u(),x())}function x(e){e=e||{};void 0!==e.cookieExpire&&(c=v(e.cookieExpire)),!0===e.sitewide&&(s=";path=/"),void 0!==e.cookieDomain&&(a=";domain="+e.cookieDomain),void 0!==e.cookieName&&(d=e.cookieName),document.cookie=d+"=true"+c+a+s,f.removeEventListener("mouseleave",k),f.removeEventListener("mouseenter",p),f.removeEventListener("keydown",E)}return{fire:w,disable:x,isDisabled:T}}});
!function(){"use strict";var e,t=document.querySelector(".recommendation"),n=!1;function o(){0!=document.body.scrollTop||0!=document.documentElement.scrollTop?(window.scrollBy(0,-50),e=setTimeout(o,10)):clearTimeout(e)}t&&(t.querySelector(".message button").addEventListener("click",function(){return o(),!1}),document.addEventListener("stillReading",function(e){n&&(t.style.bottom="-100%",n=!1)},!1),document.addEventListener("finishedReading",function(e){n||(t.style.bottom="0%",n=!0)},!1))}();
!function(n){"use strict";var i=n.document.documentElement;function r(){var t=i.clientHeight,e=n.innerHeight;return t<e?e:t}function s(){return n.pageYOffset||i.scrollTop}function o(t,e){var i=t.offsetHeight,n=s(),o=n+r(),t=function(t){for(var e=0,i=0;isNaN(t.offsetTop)||(e+=t.offsetTop),isNaN(t.offsetLeft)||(i+=t.offsetLeft),t=t.offsetParent;);return{top:e,left:i}}(t).top;return t+i*(e=e||0)<=o&&n<=t+i-i*e}function a(t){var e;t&&((e=new Image).onload=function(){t.src=e.src},e.src=t.getAttribute("data-url"))}function t(t,e){this.el=t,this.options=function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}(this.defaults,e),this._init()}t.prototype={defaults:{minDuration:0,maxDuration:0,viewportFactor:0},_init:function(){this.items=Array.prototype.slice.call(document.querySelectorAll("#"+this.el.id+" > article")),this.itemsCount=this.items.length,this.itemsRenderedCount=0,this.didScroll=!1;var i=this;i.items.forEach(function(t,e){o(t)&&(i._checkTotalRendered(),classie.add(t,"shown"),a(t.querySelector(".preload")))}),n.addEventListener("scroll",function(){i._onScrollFn()},!1),n.addEventListener("resize",function(){i._resizeHandler()},!1)},_onScrollFn:function(){var t=this;this.didScroll||(this.didScroll=!0,setTimeout(function(){t._scrollPage()},60))},_scrollPage:function(){var i=this;this.items.forEach(function(e,t){classie.has(e,"shown")||classie.has(e,"animate")||!o(e,i.options.viewportFactor)||setTimeout(function(){var t=s()+r()/2;i.el.style.WebkitPerspectiveOrigin="50% "+t+"px",i.el.style.MozPerspectiveOrigin="50% "+t+"px",i.el.style.perspectiveOrigin="50% "+t+"px",i._checkTotalRendered(),i.options.minDuration&&i.options.maxDuration&&(t=Math.random()*(i.options.maxDuration-i.options.minDuration)+i.options.minDuration+"s",e.style.WebkitAnimationDuration=t,e.style.MozAnimationDuration=t,e.style.animationDuration=t),classie.add(e,"animate"),a(e.querySelector(".preload"))},25)}),this.didScroll=!1},_resizeHandler:function(){var t=this;this.resizeTimeout&&clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(function(){t._scrollPage(),t.resizeTimeout=null},1e3)},_checkTotalRendered:function(){++this.itemsRenderedCount,this.itemsRenderedCount===this.itemsCount&&n.removeEventListener("scroll",this._onScrollFn)}},n.AnimOnScroll=t}(window);
!function(c){c.fn.simpleJekyllSearch=function(e){var l=c.extend({jsonFile:"/search.json",jsonFormat:"title,tags,categories,url,date",template:'<li><article><a href="{url}"><span class="entry-category">{categories}</span> {title} <span class="entry-date"><time datetime="{date}">{date}</time></span></a></article></li>',searchResults:".search-results",limit:"10",noResults:"<p>Oh no! We didn't find anything :(</p>"},e),a=l.jsonFormat.split(","),o=[],s=this,i=c(l.searchResults);function r(){i.children().remove()}l.jsonFile.length&&i.length&&c.ajax({type:"GET",url:l.jsonFile,dataType:"json",success:function(e,t,n){o=e,s.keyup(function(e){var t,n,s;c(this).val().length?(n=c(this).val(),s=[],c.each(o,function(e,t){for(e=0;e<a.length;e++)void 0!==t[a[e]]&&-1!==t[a[e]].toLowerCase().indexOf(n.toLowerCase())&&(s.push(t),e=a.length)}),t=s,r(),i.append(c(l.searchResultsTitle)),t.length?c.each(t,function(e,t){if(e<l.limit){for(var n=l.template,e=0;e<a.length;e++)var s=new RegExp("{"+a[e]+"}","g"),n=n.replace(s,t[a[e]]);i.append(c(n))}}):i.append(l.noResults)):r()})},error:function(e,t,n){console.log("***ERROR in simpleJekyllSearch.js***"),console.log(e),console.log(t),console.log(n)}})}}(Zepto);
!function(e,t){"function"==typeof define&&define.amd?define([],t(e)):"object"==typeof exports?module.exports=t(e):e.smoothScroll=t(e)}("undefined"!=typeof global?global:this.window||this.global,function(m){"use strict";function g(){var e={},t=!1,n=0,o=arguments.length;for("[object Boolean]"===Object.prototype.toString.call(arguments[0])&&(t=arguments[0],n++);n<o;n++){r=void 0;var r,a=arguments[n];for(r in a)Object.prototype.hasOwnProperty.call(a,r)&&(t&&"[object Object]"===Object.prototype.toString.call(a[r])?e[r]=g(!0,e[r],a[r]):e[r]=a[r])}return e}function b(e){return null===e?0:Math.max(e.scrollHeight,e.offsetHeight,e.clientHeight)+e.offsetTop}function t(e){var t=function(e,t){var n,o,r=t.charAt(0),a="classList"in document.documentElement;for("["===r&&1<(n=(t=t.substr(1,t.length-2)).split("=")).length&&(o=!0,n[1]=n[1].replace(/"/g,"").replace(/'/g,""));e&&e!==document;e=e.parentNode){if("."===r)if(a){if(e.classList.contains(t.substr(1)))return e}else if(new RegExp("(^|\\s)"+t.substr(1)+"(\\s|$)").test(e.className))return e;if("#"===r&&e.id===t.substr(1))return e;if("["===r&&e.hasAttribute(n[0])){if(!o)return e;if(e.getAttribute(n[0])===n[1])return e}if(e.tagName.toLowerCase()===t)return e}return null}(e.target,o.selector);t&&"a"===t.tagName.toLowerCase()&&(e.preventDefault(),a.animateScroll(t,t.hash,o))}function n(e){r=r||setTimeout(function(){r=null,y=b(v)},66)}var o,r,v,y,a={},c="querySelector"in document&&"addEventListener"in m,O={selector:"[data-scroll]",selectorHeader:"[data-scroll-header]",speed:500,easing:"easeInOutCubic",offset:0,updateURL:!0,callback:function(){}};a.animateScroll=function(r,a,e){function t(){var e,t,n;c=1<(c=(h+=16)/parseInt(i.speed,10))?1:c,c=s+f*(e=i.easing,t=c,"easeInQuad"===e&&(n=t*t),"easeOutQuad"===e&&(n=t*(2-t)),"easeInOutQuad"===e&&(n=t<.5?2*t*t:(4-2*t)*t-1),"easeInCubic"===e&&(n=t*t*t),"easeOutCubic"===e&&(n=--t*t*t+1),"easeInOutCubic"===e&&(n=t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1),"easeInQuart"===e&&(n=t*t*t*t),"easeOutQuart"===e&&(n=1- --t*t*t*t),"easeInOutQuart"===e&&(n=t<.5?8*t*t*t*t:1-8*--t*t*t*t),"easeInQuint"===e&&(n=t*t*t*t*t),"easeOutQuint"===e&&(n=1+--t*t*t*t*t),(n="easeInOutQuint"===e?t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t:n)||t),m.scrollTo(0,Math.floor(c)),p(c,l,o)}var o,c,n=(n=r?r.getAttribute("data-options"):null)&&"object"==typeof JSON&&"function"==typeof JSON.parse?JSON.parse(n):{},i=g(i||O,e||{},n),u="#"===(a="#"+function(e){for(var t,n=String(e),o=n.length,r=-1,a="",c=n.charCodeAt(0);++r<o;){if(0===(t=n.charCodeAt(r)))throw new InvalidCharacterError("Invalid character: the input contains U+0000.");1<=t&&t<=31||127==t||0===r&&48<=t&&t<=57||1===r&&48<=t&&t<=57&&45===c?a+="\\"+t.toString(16)+" ":a+=128<=t||45===t||95===t||48<=t&&t<=57||65<=t&&t<=90||97<=t&&t<=122?n.charAt(r):"\\"+n.charAt(r)}return a}(a.substr(1)))?m.document.documentElement:m.document.querySelector(a),s=m.pageYOffset,l=(v=v||m.document.querySelector(i.selectorHeader),y=y||b(v),function(e,t,n){var o=0;if(e.offsetParent)for(;o+=e.offsetTop,e=e.offsetParent;);return 0<=(o=o-t-n)?o:0}(u,y,parseInt(i.offset,10))),f=l-s,d=Math.max(m.document.body.scrollHeight,m.document.documentElement.scrollHeight,m.document.body.offsetHeight,m.document.documentElement.offsetHeight,m.document.body.clientHeight,m.document.documentElement.clientHeight),h=0,p=(e=a,n=i.updateURL,m.history.pushState&&(n||"true"===n)&&"file:"!==m.location.protocol&&m.history.pushState(null,null,[m.location.protocol,"//",m.location.host,m.location.pathname,m.location.search,e].join("")),function(e,t,n){var o=m.pageYOffset;(e==t||o==t||m.innerHeight+o>=d)&&(clearInterval(n),u.focus(),i.callback(r,a))});0===m.pageYOffset&&m.scrollTo(0,0),o=setInterval(t,16)};return a.destroy=function(){o&&(m.document.removeEventListener("click",t,!1),m.removeEventListener("resize",n,!1),y=v=r=o=null)},a.init=function(e){c&&(a.destroy(),o=g(O,e||{}),v=m.document.querySelector(o.selectorHeader),y=b(v),m.document.addEventListener("click",t,!1),v&&m.addEventListener("resize",n,!1))},a});
!function(){for(var n=document.links,o=0,t=n.length;o<t;o++)n[o].hostname!=window.location.hostname&&(n[o].target="_blank")}();
!function(){"use strict";var o,s,c,a,d,u,t=document.querySelector(".post-content"),m=document.querySelector(".time-bar"),l=!0;t&&m&&(o=0,s=t.scrollHeight,c=m.querySelector(".completed"),a=m.querySelector(".remaining"),d=m.querySelector(".time-completed"),u=m.querySelector(".time-remaining"),document.addEventListener("scroll",function(){var t,e,n,r,i=window.pageYOffset||document.documentElement.scrollTop;m.style.bottom=o<i&&l?"0%":"-100%",i<=s?(t=(100*(r=i/s)).toFixed(2),n=100-parseFloat(t),c.style.width=t.toString()+"%",a.style.width=n.toString()+"%",t=60*parseInt(m.getAttribute("data-minutes")),n=parseInt(r*t),r=parseInt(n/60),e=parseInt(60*(n/60-r)),t=t-n,n=parseInt(t/60),t=parseInt(60*(t/60-n)),n=n<10?"0"+n:n,t=t<10?"0"+t:t,d.innerText=(r=r<10?"0"+r:r)+":"+(e=e<10?"0"+e:e),u.innerText=n+":"+t,l=!0,(r=document.createEvent("CustomEvent")).initCustomEvent("stillReading"),document.dispatchEvent(r)):(c.style.width="100%",a.style.width="0%",e=(e=parseInt(m.getAttribute("data-minutes")))<10?"0"+e:e,d.innerText="00:00",u.innerText=e+":00",l=!1,(n=document.createEvent("CustomEvent")).initCustomEvent("finishedReading"),document.dispatchEvent(n)),o=i}))}();
!function(o,e){o("#menu").click(function(){o("body").addClass("push-menu-to-right"),o("#sidebar").addClass("open"),o(".overlay").addClass("show")}),o("#mask").click(function(){o("body").removeClass("push-menu-to-right"),o("#sidebar").removeClass("open"),o(".overlay").removeClass("show")}),o(e).scroll(function(){0<o(this).scrollTop()?o("body").addClass("light"):o("body").removeClass("light")});var s=o(".modal .close"),a=(s.on("click",function(){s.parent().parent().addClass("closed")}),o(".modal.exit")),c=(a.length&&ouibounce(a[0],{aggressive:!0,callback:function(){a.find(".close").on("click",function(){a.hide()})}}),{close:o(".icon-remove-sign"),searchform:o(".search-form"),canvas:o("body"),dothis:o(".dosearch")});function l(){o(".search-wrapper").toggleClass("active"),c.searchform.toggleClass("active"),c.canvas.removeClass("search-overlay")}c.dothis.on("click",function(){o(".search-wrapper").toggleClass("active"),c.searchform.toggleClass("active"),c.searchform.find("input").focus(),c.canvas.toggleClass("search-overlay"),o(".search-field").simpleJekyllSearch()}),c.close.on("click",l),document.addEventListener("keyup",function(e){27==e.keyCode&&o(".search-overlay").length&&l()}),1<=document.getElementsByClassName("home").length&&new AnimOnScroll(document.getElementById("grid"),{minDuration:.4,maxDuration:.7,viewportFactor:.2}),smoothScroll.init({selectorHeader:".bar-header",speed:500,updateURL:!1})}(Zepto,window);
//# sourceMappingURL=scripts.min.js.map
