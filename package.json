{"name": "jekflix-template", "version": "3.1.2", "description": "A Jekyll theme inspired by Netflix", "type": "module", "main": "gulpfile.js", "homepage": "https://github.com/thiagorossener/jekflix-template.git#readme", "repository": {"type": "git", "url": "https://github.com/thiagorossener/jekflix-template.git"}, "keywords": ["j<PERSON><PERSON><PERSON>", "template", "theme", "blog", "netlify"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/thiagorossener/jekflix-template/issues"}, "devDependencies": {"browser-sync": "^2.26.7", "child_process": "^1.0.2", "del": "^7.0.0", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "gulp-imagemin": "^8.0.0", "gulp-include": "^2.4.1", "gulp-plumber": "^1.1.0", "gulp-rename": "^2.0.0", "gulp-sourcemaps": "^3.0.0", "gulp-uglify": "^3.0.0", "gulp-yaml": "^2.0.4", "json-sass": "^1.3.5", "lodash": "^4.17.21", "lodash.merge": "^4.6.2", "vinyl-source-stream": "^2.0.0"}, "engines": {"node": "^18"}}