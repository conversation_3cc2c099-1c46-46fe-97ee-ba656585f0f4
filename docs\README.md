# Home

Welcome to the *Jekflix Template* docs!

Here you will find everything you need to get your website up and running with the template:

- How to setup your local [environment](setup.md#environment) and [install](setup.md#installing-template) the template
- How to [customize](settings.md#settings) your website, changing theme colors, layout, titles, etc.
- How to [deploy](netlify-cms.md#deploy-with-netlify) your website using Netlify and edit content using [Netlify CMS](netlify-cms.md#integrate-with-netlify-cms)

*and the list goes on...*

Navigate through the menu below as you wish, and most of all, have fun! 😄

## Summary

* [Features](features.md#features)
* [Setup](setup.md#setup)
  * [Environment](setup.md#environment)
  * [Installing template](setup.md#installing-template)
  * [Running local](setup.md#running-local)
  * [Customization](setup.md#customization)
  * [Translations](setup.md#translations)
* [Settings](settings.md#settings)
  * [Site](settings.md#site)
  * [Social](settings.md#social)
  * [Theme](settings.md#theme)
  * [Posts](settings.md#posts)
  * [Advanced](settings.md#advanced)
* [Post](post.md#post)
  * [Creating a post](post.md#creating-a-post.md)
  * [Front Matter properties](post.md#front-matter-properties)
* [Netlify CMS](netlify-cms.md#netlify-cms)
  * [Deploy with Netlify](netlify-cms.md#deploy-with-netlify)
  * [Integrate with Netlify CMS](netlify-cms.md#integrate-with-netlify-cms)



