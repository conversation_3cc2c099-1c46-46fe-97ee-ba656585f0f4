!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t(e.ReactColor={},e.React)}(this,function(e,t){"use strict";var r="default"in t?t.default:t,n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function a(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function o(e,t){return e(t={exports:{}},t.exports),t.exports}var i="object"==typeof n&&n&&n.Object===Object&&n,l="object"==typeof self&&self&&self.Object===Object&&self,s=i||l||Function("return this")(),c=s.Symbol,u=Object.prototype,p=u.hasOwnProperty,h=u.toString,f=c?c.toStringTag:void 0;var d=function(e){var t=p.call(e,f),r=e[f];try{e[f]=void 0;var n=!0}catch(e){}var a=h.call(e);return n&&(t?e[f]=r:delete e[f]),a},b=Object.prototype.toString;var g=function(e){return b.call(e)},v="[object Null]",x="[object Undefined]",y=c?c.toStringTag:void 0;var m=function(e){return null==e?void 0===e?x:v:y&&y in Object(e)?d(e):g(e)},w=Array.isArray;var E=function(e){return null!=e&&"object"==typeof e},_="[object String]";var C=function(e){return"string"==typeof e||!w(e)&&E(e)&&m(e)==_};var S=function(e){return function(t,r,n){for(var a=-1,o=Object(t),i=n(t),l=i.length;l--;){var s=i[e?l:++a];if(!1===r(o[s],s,o))break}return t}}();var k=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n},j="[object Arguments]";var O=function(e){return E(e)&&m(e)==j},R=Object.prototype,A=R.hasOwnProperty,F=R.propertyIsEnumerable,B=O(function(){return arguments}())?O:function(e){return E(e)&&A.call(e,"callee")&&!F.call(e,"callee")};var M=function(){return!1},H=o(function(e,t){var r=t&&!t.nodeType&&t,n=r&&e&&!e.nodeType&&e,a=n&&n.exports===r?s.Buffer:void 0,o=(a?a.isBuffer:void 0)||M;e.exports=o}),P=9007199254740991,T=/^(?:0|[1-9]\d*)$/;var D=function(e,t){var r=typeof e;return!!(t=null==t?P:t)&&("number"==r||"symbol"!=r&&T.test(e))&&e>-1&&e%1==0&&e<t},z=9007199254740991;var L=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=z},N={};N["[object Float32Array]"]=N["[object Float64Array]"]=N["[object Int8Array]"]=N["[object Int16Array]"]=N["[object Int32Array]"]=N["[object Uint8Array]"]=N["[object Uint8ClampedArray]"]=N["[object Uint16Array]"]=N["[object Uint32Array]"]=!0,N["[object Arguments]"]=N["[object Array]"]=N["[object ArrayBuffer]"]=N["[object Boolean]"]=N["[object DataView]"]=N["[object Date]"]=N["[object Error]"]=N["[object Function]"]=N["[object Map]"]=N["[object Number]"]=N["[object Object]"]=N["[object RegExp]"]=N["[object Set]"]=N["[object String]"]=N["[object WeakMap]"]=!1;var G=function(e){return E(e)&&L(e.length)&&!!N[m(e)]};var U=function(e){return function(t){return e(t)}},I=o(function(e,t){var r=t&&!t.nodeType&&t,n=r&&e&&!e.nodeType&&e,a=n&&n.exports===r&&i.process,o=function(){try{var e=n&&n.require&&n.require("util").types;return e||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=o}),W=I&&I.isTypedArray,X=W?U(W):G,V=Object.prototype.hasOwnProperty;var $=function(e,t){var r=w(e),n=!r&&B(e),a=!r&&!n&&H(e),o=!r&&!n&&!a&&X(e),i=r||n||a||o,l=i?k(e.length,String):[],s=l.length;for(var c in e)!t&&!V.call(e,c)||i&&("length"==c||a&&("offset"==c||"parent"==c)||o&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||D(c,s))||l.push(c);return l},q=Object.prototype;var Y=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||q)};var K=function(e,t){return function(r){return e(t(r))}},Z=K(Object.keys,Object),J=Object.prototype.hasOwnProperty;var Q=function(e){if(!Y(e))return Z(e);var t=[];for(var r in Object(e))J.call(e,r)&&"constructor"!=r&&t.push(r);return t};var ee=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},te="[object AsyncFunction]",re="[object Function]",ne="[object GeneratorFunction]",ae="[object Proxy]";var oe=function(e){if(!ee(e))return!1;var t=m(e);return t==re||t==ne||t==te||t==ae};var ie=function(e){return null!=e&&L(e.length)&&!oe(e)};var le=function(e){return ie(e)?$(e):Q(e)};var se=function(e,t){return e&&S(e,t,le)};var ce=function(e){return e};var ue=function(e){return"function"==typeof e?e:ce};var pe=function(e,t){return e&&se(e,ue(t))},he=K(Object.getPrototypeOf,Object),fe="[object Object]",de=Function.prototype,be=Object.prototype,ge=de.toString,ve=be.hasOwnProperty,xe=ge.call(Object);var ye=function(e){if(!E(e)||m(e)!=fe)return!1;var t=he(e);if(null===t)return!0;var r=ve.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&ge.call(r)==xe};var me=function(e,t){for(var r=-1,n=null==e?0:e.length,a=Array(n);++r<n;)a[r]=t(e[r],r,e);return a};var we=function(){this.__data__=[],this.size=0};var Ee=function(e,t){return e===t||e!=e&&t!=t};var _e=function(e,t){for(var r=e.length;r--;)if(Ee(e[r][0],t))return r;return-1},Ce=Array.prototype.splice;var Se=function(e){var t=this.__data__,r=_e(t,e);return!(r<0||(r==t.length-1?t.pop():Ce.call(t,r,1),--this.size,0))};var ke=function(e){var t=this.__data__,r=_e(t,e);return r<0?void 0:t[r][1]};var je=function(e){return _e(this.__data__,e)>-1};var Oe=function(e,t){var r=this.__data__,n=_e(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this};function Re(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Re.prototype.clear=we,Re.prototype.delete=Se,Re.prototype.get=ke,Re.prototype.has=je,Re.prototype.set=Oe;var Ae=Re;var Fe=function(){this.__data__=new Ae,this.size=0};var Be=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r};var Me=function(e){return this.__data__.get(e)};var He,Pe=function(e){return this.__data__.has(e)},Te=s["__core-js_shared__"],De=(He=/[^.]+$/.exec(Te&&Te.keys&&Te.keys.IE_PROTO||""))?"Symbol(src)_1."+He:"";var ze=function(e){return!!De&&De in e},Le=Function.prototype.toString;var Ne=function(e){if(null!=e){try{return Le.call(e)}catch(e){}try{return e+""}catch(e){}}return""},Ge=/^\[object .+?Constructor\]$/,Ue=Function.prototype,Ie=Object.prototype,We=Ue.toString,Xe=Ie.hasOwnProperty,Ve=RegExp("^"+We.call(Xe).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var $e=function(e){return!(!ee(e)||ze(e))&&(oe(e)?Ve:Ge).test(Ne(e))};var qe=function(e,t){return null==e?void 0:e[t]};var Ye=function(e,t){var r=qe(e,t);return $e(r)?r:void 0},Ke=Ye(s,"Map"),Ze=Ye(Object,"create");var Je=function(){this.__data__=Ze?Ze(null):{},this.size=0};var Qe=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},et="__lodash_hash_undefined__",tt=Object.prototype.hasOwnProperty;var rt=function(e){var t=this.__data__;if(Ze){var r=t[e];return r===et?void 0:r}return tt.call(t,e)?t[e]:void 0},nt=Object.prototype.hasOwnProperty;var at=function(e){var t=this.__data__;return Ze?void 0!==t[e]:nt.call(t,e)},ot="__lodash_hash_undefined__";var it=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Ze&&void 0===t?ot:t,this};function lt(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}lt.prototype.clear=Je,lt.prototype.delete=Qe,lt.prototype.get=rt,lt.prototype.has=at,lt.prototype.set=it;var st=lt;var ct=function(){this.size=0,this.__data__={hash:new st,map:new(Ke||Ae),string:new st}};var ut=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var pt=function(e,t){var r=e.__data__;return ut(t)?r["string"==typeof t?"string":"hash"]:r.map};var ht=function(e){var t=pt(this,e).delete(e);return this.size-=t?1:0,t};var ft=function(e){return pt(this,e).get(e)};var dt=function(e){return pt(this,e).has(e)};var bt=function(e,t){var r=pt(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this};function gt(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}gt.prototype.clear=ct,gt.prototype.delete=ht,gt.prototype.get=ft,gt.prototype.has=dt,gt.prototype.set=bt;var vt=gt,xt=200;var yt=function(e,t){var r=this.__data__;if(r instanceof Ae){var n=r.__data__;if(!Ke||n.length<xt-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new vt(n)}return r.set(e,t),this.size=r.size,this};function mt(e){var t=this.__data__=new Ae(e);this.size=t.size}mt.prototype.clear=Fe,mt.prototype.delete=Be,mt.prototype.get=Me,mt.prototype.has=Pe,mt.prototype.set=yt;var wt=mt,Et="__lodash_hash_undefined__";var _t=function(e){return this.__data__.set(e,Et),this};var Ct=function(e){return this.__data__.has(e)};function St(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new vt;++t<r;)this.add(e[t])}St.prototype.add=St.prototype.push=_t,St.prototype.has=Ct;var kt=St;var jt=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1};var Ot=function(e,t){return e.has(t)},Rt=1,At=2;var Ft=function(e,t,r,n,a,o){var i=r&Rt,l=e.length,s=t.length;if(l!=s&&!(i&&s>l))return!1;var c=o.get(e);if(c&&o.get(t))return c==t;var u=-1,p=!0,h=r&At?new kt:void 0;for(o.set(e,t),o.set(t,e);++u<l;){var f=e[u],d=t[u];if(n)var b=i?n(d,f,u,t,e,o):n(f,d,u,e,t,o);if(void 0!==b){if(b)continue;p=!1;break}if(h){if(!jt(t,function(e,t){if(!Ot(h,t)&&(f===e||a(f,e,r,n,o)))return h.push(t)})){p=!1;break}}else if(f!==d&&!a(f,d,r,n,o)){p=!1;break}}return o.delete(e),o.delete(t),p},Bt=s.Uint8Array;var Mt=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e,n){r[++t]=[n,e]}),r};var Ht=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r},Pt=1,Tt=2,Dt="[object Boolean]",zt="[object Date]",Lt="[object Error]",Nt="[object Map]",Gt="[object Number]",Ut="[object RegExp]",It="[object Set]",Wt="[object String]",Xt="[object Symbol]",Vt="[object ArrayBuffer]",$t="[object DataView]",qt=c?c.prototype:void 0,Yt=qt?qt.valueOf:void 0;var Kt=function(e,t,r,n,a,o,i){switch(r){case $t:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case Vt:return!(e.byteLength!=t.byteLength||!o(new Bt(e),new Bt(t)));case Dt:case zt:case Gt:return Ee(+e,+t);case Lt:return e.name==t.name&&e.message==t.message;case Ut:case Wt:return e==t+"";case Nt:var l=Mt;case It:var s=n&Pt;if(l||(l=Ht),e.size!=t.size&&!s)return!1;var c=i.get(e);if(c)return c==t;n|=Tt,i.set(e,t);var u=Ft(l(e),l(t),n,a,o,i);return i.delete(e),u;case Xt:if(Yt)return Yt.call(e)==Yt.call(t)}return!1};var Zt=function(e,t){for(var r=-1,n=t.length,a=e.length;++r<n;)e[a+r]=t[r];return e};var Jt=function(e,t,r){var n=t(e);return w(e)?n:Zt(n,r(e))};var Qt=function(e,t){for(var r=-1,n=null==e?0:e.length,a=0,o=[];++r<n;){var i=e[r];t(i,r,e)&&(o[a++]=i)}return o};var er=function(){return[]},tr=Object.prototype.propertyIsEnumerable,rr=Object.getOwnPropertySymbols,nr=rr?function(e){return null==e?[]:(e=Object(e),Qt(rr(e),function(t){return tr.call(e,t)}))}:er;var ar=function(e){return Jt(e,le,nr)},or=1,ir=Object.prototype.hasOwnProperty;var lr=function(e,t,r,n,a,o){var i=r&or,l=ar(e),s=l.length;if(s!=ar(t).length&&!i)return!1;for(var c=s;c--;){var u=l[c];if(!(i?u in t:ir.call(t,u)))return!1}var p=o.get(e);if(p&&o.get(t))return p==t;var h=!0;o.set(e,t),o.set(t,e);for(var f=i;++c<s;){var d=e[u=l[c]],b=t[u];if(n)var g=i?n(b,d,u,t,e,o):n(d,b,u,e,t,o);if(!(void 0===g?d===b||a(d,b,r,n,o):g)){h=!1;break}f||(f="constructor"==u)}if(h&&!f){var v=e.constructor,x=t.constructor;v!=x&&"constructor"in e&&"constructor"in t&&!("function"==typeof v&&v instanceof v&&"function"==typeof x&&x instanceof x)&&(h=!1)}return o.delete(e),o.delete(t),h},sr=Ye(s,"DataView"),cr=Ye(s,"Promise"),ur=Ye(s,"Set"),pr=Ye(s,"WeakMap"),hr=Ne(sr),fr=Ne(Ke),dr=Ne(cr),br=Ne(ur),gr=Ne(pr),vr=m;(sr&&"[object DataView]"!=vr(new sr(new ArrayBuffer(1)))||Ke&&"[object Map]"!=vr(new Ke)||cr&&"[object Promise]"!=vr(cr.resolve())||ur&&"[object Set]"!=vr(new ur)||pr&&"[object WeakMap]"!=vr(new pr))&&(vr=function(e){var t=m(e),r="[object Object]"==t?e.constructor:void 0,n=r?Ne(r):"";if(n)switch(n){case hr:return"[object DataView]";case fr:return"[object Map]";case dr:return"[object Promise]";case br:return"[object Set]";case gr:return"[object WeakMap]"}return t});var xr=vr,yr=1,mr="[object Arguments]",wr="[object Array]",Er="[object Object]",_r=Object.prototype.hasOwnProperty;var Cr=function(e,t,r,n,a,o){var i=w(e),l=w(t),s=i?wr:xr(e),c=l?wr:xr(t),u=(s=s==mr?Er:s)==Er,p=(c=c==mr?Er:c)==Er,h=s==c;if(h&&H(e)){if(!H(t))return!1;i=!0,u=!1}if(h&&!u)return o||(o=new wt),i||X(e)?Ft(e,t,r,n,a,o):Kt(e,t,s,r,n,a,o);if(!(r&yr)){var f=u&&_r.call(e,"__wrapped__"),d=p&&_r.call(t,"__wrapped__");if(f||d){var b=f?e.value():e,g=d?t.value():t;return o||(o=new wt),a(b,g,r,n,o)}}return!!h&&(o||(o=new wt),lr(e,t,r,n,a,o))};var Sr=function e(t,r,n,a,o){return t===r||(null==t||null==r||!E(t)&&!E(r)?t!=t&&r!=r:Cr(t,r,n,a,e,o))},kr=1,jr=2;var Or=function(e,t,r,n){var a=r.length,o=a,i=!n;if(null==e)return!o;for(e=Object(e);a--;){var l=r[a];if(i&&l[2]?l[1]!==e[l[0]]:!(l[0]in e))return!1}for(;++a<o;){var s=(l=r[a])[0],c=e[s],u=l[1];if(i&&l[2]){if(void 0===c&&!(s in e))return!1}else{var p=new wt;if(n)var h=n(c,u,s,e,t,p);if(!(void 0===h?Sr(u,c,kr|jr,n,p):h))return!1}}return!0};var Rr=function(e){return e==e&&!ee(e)};var Ar=function(e){for(var t=le(e),r=t.length;r--;){var n=t[r],a=e[n];t[r]=[n,a,Rr(a)]}return t};var Fr=function(e,t){return function(r){return null!=r&&r[e]===t&&(void 0!==t||e in Object(r))}};var Br=function(e){var t=Ar(e);return 1==t.length&&t[0][2]?Fr(t[0][0],t[0][1]):function(r){return r===e||Or(r,e,t)}},Mr="[object Symbol]";var Hr=function(e){return"symbol"==typeof e||E(e)&&m(e)==Mr},Pr=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Tr=/^\w*$/;var Dr=function(e,t){if(w(e))return!1;var r=typeof e;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=e&&!Hr(e))||Tr.test(e)||!Pr.test(e)||null!=t&&e in Object(t)},zr="Expected a function";function Lr(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError(zr);var r=function(){var n=arguments,a=t?t.apply(this,n):n[0],o=r.cache;if(o.has(a))return o.get(a);var i=e.apply(this,n);return r.cache=o.set(a,i)||o,i};return r.cache=new(Lr.Cache||vt),r}Lr.Cache=vt;var Nr=Lr,Gr=500;var Ur=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Ir=/\\(\\)?/g,Wr=function(e){var t=Nr(e,function(e){return r.size===Gr&&r.clear(),e}),r=t.cache;return t}(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Ur,function(e,r,n,a){t.push(n?a.replace(Ir,"$1"):r||e)}),t}),Xr=1/0,Vr=c?c.prototype:void 0,$r=Vr?Vr.toString:void 0;var qr=function e(t){if("string"==typeof t)return t;if(w(t))return me(t,e)+"";if(Hr(t))return $r?$r.call(t):"";var r=t+"";return"0"==r&&1/t==-Xr?"-0":r};var Yr=function(e){return null==e?"":qr(e)};var Kr=function(e,t){return w(e)?e:Dr(e,t)?[e]:Wr(Yr(e))},Zr=1/0;var Jr=function(e){if("string"==typeof e||Hr(e))return e;var t=e+"";return"0"==t&&1/e==-Zr?"-0":t};var Qr=function(e,t){for(var r=0,n=(t=Kr(t,e)).length;null!=e&&r<n;)e=e[Jr(t[r++])];return r&&r==n?e:void 0};var en=function(e,t,r){var n=null==e?void 0:Qr(e,t);return void 0===n?r:n};var tn=function(e,t){return null!=e&&t in Object(e)};var rn=function(e,t,r){for(var n=-1,a=(t=Kr(t,e)).length,o=!1;++n<a;){var i=Jr(t[n]);if(!(o=null!=e&&r(e,i)))break;e=e[i]}return o||++n!=a?o:!!(a=null==e?0:e.length)&&L(a)&&D(i,a)&&(w(e)||B(e))};var nn=function(e,t){return null!=e&&rn(e,t,tn)},an=1,on=2;var ln=function(e,t){return Dr(e)&&Rr(t)?Fr(Jr(e),t):function(r){var n=en(r,e);return void 0===n&&n===t?nn(r,e):Sr(t,n,an|on)}};var sn=function(e){return function(t){return null==t?void 0:t[e]}};var cn=function(e){return function(t){return Qr(t,e)}};var un=function(e){return Dr(e)?sn(Jr(e)):cn(e)};var pn=function(e){return"function"==typeof e?e:null==e?ce:"object"==typeof e?w(e)?ln(e[0],e[1]):Br(e):un(e)};var hn=function(e,t){return function(r,n){if(null==r)return r;if(!ie(r))return e(r,n);for(var a=r.length,o=t?a:-1,i=Object(r);(t?o--:++o<a)&&!1!==n(i[o],o,i););return r}}(se);var fn=function(e,t){var r=-1,n=ie(e)?Array(e.length):[];return hn(e,function(e,a,o){n[++r]=t(e,a,o)}),n};var dn=function(e,t){return(w(e)?me:fn)(e,pn(t,3))},bn=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.flattenNames=void 0;var r=i(C),n=i(pe),a=i(ye),o=i(dn);function i(e){return e&&e.__esModule?e:{default:e}}var l=t.flattenNames=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=[];return(0,o.default)(t,function(t){Array.isArray(t)?e(t).map(function(e){return i.push(e)}):(0,a.default)(t)?(0,n.default)(t,function(e,t){!0===e&&i.push(t),i.push(t+"-"+e)}):(0,r.default)(t)&&i.push(t)}),i};t.default=l});a(bn);bn.flattenNames;var gn=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n&&!1!==t(e[r],r,e););return e},vn=function(){try{var e=Ye(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();var xn=function(e,t,r){"__proto__"==t&&vn?vn(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r},yn=Object.prototype.hasOwnProperty;var mn=function(e,t,r){var n=e[t];yn.call(e,t)&&Ee(n,r)&&(void 0!==r||t in e)||xn(e,t,r)};var wn=function(e,t,r,n){var a=!r;r||(r={});for(var o=-1,i=t.length;++o<i;){var l=t[o],s=n?n(r[l],e[l],l,r,e):void 0;void 0===s&&(s=e[l]),a?xn(r,l,s):mn(r,l,s)}return r};var En=function(e,t){return e&&wn(t,le(t),e)};var _n=function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t},Cn=Object.prototype.hasOwnProperty;var Sn=function(e){if(!ee(e))return _n(e);var t=Y(e),r=[];for(var n in e)("constructor"!=n||!t&&Cn.call(e,n))&&r.push(n);return r};var kn=function(e){return ie(e)?$(e,!0):Sn(e)};var jn=function(e,t){return e&&wn(t,kn(t),e)},On=o(function(e,t){var r=t&&!t.nodeType&&t,n=r&&e&&!e.nodeType&&e,a=n&&n.exports===r?s.Buffer:void 0,o=a?a.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var r=e.length,n=o?o(r):new e.constructor(r);return e.copy(n),n}});var Rn=function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t};var An=function(e,t){return wn(e,nr(e),t)},Fn=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)Zt(t,nr(e)),e=he(e);return t}:er;var Bn=function(e,t){return wn(e,Fn(e),t)};var Mn=function(e){return Jt(e,kn,Fn)},Hn=Object.prototype.hasOwnProperty;var Pn=function(e){var t=e.length,r=new e.constructor(t);return t&&"string"==typeof e[0]&&Hn.call(e,"index")&&(r.index=e.index,r.input=e.input),r};var Tn=function(e){var t=new e.constructor(e.byteLength);return new Bt(t).set(new Bt(e)),t};var Dn=function(e,t){var r=t?Tn(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)},zn=/\w*$/;var Ln=function(e){var t=new e.constructor(e.source,zn.exec(e));return t.lastIndex=e.lastIndex,t},Nn=c?c.prototype:void 0,Gn=Nn?Nn.valueOf:void 0;var Un=function(e){return Gn?Object(Gn.call(e)):{}};var In=function(e,t){var r=t?Tn(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)},Wn="[object Boolean]",Xn="[object Date]",Vn="[object Map]",$n="[object Number]",qn="[object RegExp]",Yn="[object Set]",Kn="[object String]",Zn="[object Symbol]",Jn="[object ArrayBuffer]",Qn="[object DataView]",ea="[object Float32Array]",ta="[object Float64Array]",ra="[object Int8Array]",na="[object Int16Array]",aa="[object Int32Array]",oa="[object Uint8Array]",ia="[object Uint8ClampedArray]",la="[object Uint16Array]",sa="[object Uint32Array]";var ca=function(e,t,r){var n=e.constructor;switch(t){case Jn:return Tn(e);case Wn:case Xn:return new n(+e);case Qn:return Dn(e,r);case ea:case ta:case ra:case na:case aa:case oa:case ia:case la:case sa:return In(e,r);case Vn:return new n;case $n:case Kn:return new n(e);case qn:return Ln(e);case Yn:return new n;case Zn:return Un(e)}},ua=Object.create,pa=function(){function e(){}return function(t){if(!ee(t))return{};if(ua)return ua(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();var ha=function(e){return"function"!=typeof e.constructor||Y(e)?{}:pa(he(e))},fa="[object Map]";var da=function(e){return E(e)&&xr(e)==fa},ba=I&&I.isMap,ga=ba?U(ba):da,va="[object Set]";var xa=function(e){return E(e)&&xr(e)==va},ya=I&&I.isSet,ma=ya?U(ya):xa,wa=1,Ea=2,_a=4,Ca="[object Arguments]",Sa="[object Function]",ka="[object GeneratorFunction]",ja="[object Object]",Oa={};Oa[Ca]=Oa["[object Array]"]=Oa["[object ArrayBuffer]"]=Oa["[object DataView]"]=Oa["[object Boolean]"]=Oa["[object Date]"]=Oa["[object Float32Array]"]=Oa["[object Float64Array]"]=Oa["[object Int8Array]"]=Oa["[object Int16Array]"]=Oa["[object Int32Array]"]=Oa["[object Map]"]=Oa["[object Number]"]=Oa[ja]=Oa["[object RegExp]"]=Oa["[object Set]"]=Oa["[object String]"]=Oa["[object Symbol]"]=Oa["[object Uint8Array]"]=Oa["[object Uint8ClampedArray]"]=Oa["[object Uint16Array]"]=Oa["[object Uint32Array]"]=!0,Oa["[object Error]"]=Oa[Sa]=Oa["[object WeakMap]"]=!1;var Ra=function e(t,r,n,a,o,i){var l,s=r&wa,c=r&Ea,u=r&_a;if(n&&(l=o?n(t,a,o,i):n(t)),void 0!==l)return l;if(!ee(t))return t;var p=w(t);if(p){if(l=Pn(t),!s)return Rn(t,l)}else{var h=xr(t),f=h==Sa||h==ka;if(H(t))return On(t,s);if(h==ja||h==Ca||f&&!o){if(l=c||f?{}:ha(t),!s)return c?Bn(t,jn(l,t)):An(t,En(l,t))}else{if(!Oa[h])return o?t:{};l=ca(t,h,s)}}i||(i=new wt);var d=i.get(t);if(d)return d;if(i.set(t,l),ma(t))return t.forEach(function(a){l.add(e(a,r,n,a,t,i))}),l;if(ga(t))return t.forEach(function(a,o){l.set(o,e(a,r,n,o,t,i))}),l;var b=u?c?Mn:ar:c?keysIn:le,g=p?void 0:b(t);return gn(g||t,function(a,o){g&&(a=t[o=a]),mn(l,o,e(a,r,n,o,t,i))}),l},Aa=1,Fa=4;var Ba=function(e){return Ra(e,Aa|Fa)},Ma=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.mergeClasses=void 0;var r=o(pe),n=o(Ba),a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e};function o(e){return e&&e.__esModule?e:{default:e}}var i=t.mergeClasses=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=e.default&&(0,n.default)(e.default)||{};return t.map(function(t){var n=e[t];return n&&(0,r.default)(n,function(e,t){o[t]||(o[t]={}),o[t]=a({},o[t],n[t])}),t}),o};t.default=i});a(Ma);Ma.mergeClasses;var Ha=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.autoprefix=void 0;var r,n=(r=pe)&&r.__esModule?r:{default:r},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e};var o={borderRadius:function(e){return{msBorderRadius:e,MozBorderRadius:e,OBorderRadius:e,WebkitBorderRadius:e,borderRadius:e}},boxShadow:function(e){return{msBoxShadow:e,MozBoxShadow:e,OBoxShadow:e,WebkitBoxShadow:e,boxShadow:e}},userSelect:function(e){return{WebkitTouchCallout:e,KhtmlUserSelect:e,MozUserSelect:e,msUserSelect:e,WebkitUserSelect:e,userSelect:e}},flex:function(e){return{WebkitBoxFlex:e,MozBoxFlex:e,WebkitFlex:e,msFlex:e,flex:e}},flexBasis:function(e){return{WebkitFlexBasis:e,flexBasis:e}},justifyContent:function(e){return{WebkitJustifyContent:e,justifyContent:e}},transition:function(e){return{msTransition:e,MozTransition:e,OTransition:e,WebkitTransition:e,transition:e}},transform:function(e){return{msTransform:e,MozTransform:e,OTransform:e,WebkitTransform:e,transform:e}},absolute:function(e){var t=e&&e.split(" ");return{position:"absolute",top:t&&t[0],right:t&&t[1],bottom:t&&t[2],left:t&&t[3]}},extend:function(e,t){var r=t[e];return r||{extend:e}}},i=t.autoprefix=function(e){var t={};return(0,n.default)(e,function(e,r){var i={};(0,n.default)(e,function(e,t){var r=o[t];r?i=a({},i,r(e)):i[t]=e}),t[r]=i}),t};t.default=i});a(Ha);Ha.autoprefix;var Pa=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.hover=void 0;var n,a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},o=(n=r)&&n.__esModule?n:{default:n};function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var l=t.hover=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"span";return function(r){function n(){var r,l,s;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n);for(var c=arguments.length,u=Array(c),p=0;p<c;p++)u[p]=arguments[p];return l=s=i(this,(r=n.__proto__||Object.getPrototypeOf(n)).call.apply(r,[this].concat(u))),s.state={hover:!1},s.handleMouseOver=function(){return s.setState({hover:!0})},s.handleMouseOut=function(){return s.setState({hover:!1})},s.render=function(){return o.default.createElement(t,{onMouseOver:s.handleMouseOver,onMouseOut:s.handleMouseOut},o.default.createElement(e,a({},s.props,s.state)))},i(s,l)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,o.default.Component),n}()};t.default=l});a(Pa);Pa.hover;var Ta=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.active=void 0;var n,a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},o=(n=r)&&n.__esModule?n:{default:n};function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var l=t.active=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"span";return function(r){function n(){var r,l,s;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n);for(var c=arguments.length,u=Array(c),p=0;p<c;p++)u[p]=arguments[p];return l=s=i(this,(r=n.__proto__||Object.getPrototypeOf(n)).call.apply(r,[this].concat(u))),s.state={active:!1},s.handleMouseDown=function(){return s.setState({active:!0})},s.handleMouseUp=function(){return s.setState({active:!1})},s.render=function(){return o.default.createElement(t,{onMouseDown:s.handleMouseDown,onMouseUp:s.handleMouseUp},o.default.createElement(e,a({},s.props,s.state)))},i(s,l)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,o.default.Component),n}()};t.default=l});a(Ta);Ta.active;var Da=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.default=function(e,t){var r={},n=function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];r[e]=t};return 0===e&&n("first-child"),e===t-1&&n("last-child"),(0===e||e%2==0)&&n("even"),1===Math.abs(e%2)&&n("odd"),n("nth-child",e),r}});a(Da);var za=o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.ReactCSS=t.loop=t.handleActive=t.handleHover=t.hover=void 0;var r=s(bn),n=s(Ma),a=s(Ha),o=s(Pa),i=s(Ta),l=s(Da);function s(e){return e&&e.__esModule?e:{default:e}}t.hover=o.default,t.handleHover=o.default,t.handleActive=i.default,t.loop=l.default;var c=t.ReactCSS=function(e){for(var t=arguments.length,o=Array(t>1?t-1:0),i=1;i<t;i++)o[i-1]=arguments[i];var l=(0,r.default)(o),s=(0,n.default)(e,l);return(0,a.default)(s)};t.default=c}),La=a(za),Na=(za.ReactCSS,za.loop,za.handleActive,za.handleHover),Ga=(za.hover,function(e,t,r,n){e.preventDefault();var a=n.clientWidth,o=n.clientHeight,i="number"==typeof e.pageX?e.pageX:e.touches[0].pageX,l="number"==typeof e.pageY?e.pageY:e.touches[0].pageY,s=i-(n.getBoundingClientRect().left+window.pageXOffset),c=l-(n.getBoundingClientRect().top+window.pageYOffset);if("vertical"===r.direction){var u=void 0;if(u=c<0?0:c>o?1:Math.round(100*c/o)/100,r.hsl.a!==u)return{h:r.hsl.h,s:r.hsl.s,l:r.hsl.l,a:u,source:"rgb"}}else{var p=void 0;if(p=s<0?0:s>a?1:Math.round(100*s/a)/100,r.a!==p)return{h:r.hsl.h,s:r.hsl.s,l:r.hsl.l,a:p,source:"rgb"}}return null}),Ua={},Ia=function(e,t,r,n){var a=e+"-"+t+"-"+r+(n?"-server":""),o=function(e,t,r,n){if("undefined"==typeof document&&!n)return null;var a=n?new n:document.createElement("canvas");a.width=2*r,a.height=2*r;var o=a.getContext("2d");return o?(o.fillStyle=e,o.fillRect(0,0,a.width,a.height),o.fillStyle=t,o.fillRect(0,0,r,r),o.translate(r,r),o.fillRect(0,0,r,r),a.toDataURL()):null}(e,t,r,n);return Ua[a]?Ua[a]:(Ua[a]=o,o)},Wa=function(e){var t=e.white,n=e.grey,a=e.size,o=e.renderers,i=e.borderRadius,l=e.boxShadow,s=La({default:{grid:{borderRadius:i,boxShadow:l,absolute:"0px 0px 0px 0px",background:"url("+Ia(t,n,a,o.canvas)+") center left"}}});return r.createElement("div",{style:s.grid})};Wa.defaultProps={size:8,white:"transparent",grey:"rgba(0,0,0,.08)",renderers:{}};var Xa=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},Va=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),$a=function(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},qa=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ya=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)},Ka=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t},Za=function(e){function t(){var e,r,n;Xa(this,t);for(var a=arguments.length,o=Array(a),i=0;i<a;i++)o[i]=arguments[i];return r=n=Ka(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(o))),n.handleChange=function(e,t){var r=Ga(e,t,n.props,n.container);r&&n.props.onChange&&n.props.onChange(r,e)},n.handleMouseDown=function(e){n.handleChange(e,!0),window.addEventListener("mousemove",n.handleChange),window.addEventListener("mouseup",n.handleMouseUp)},n.handleMouseUp=function(){n.unbindEventListeners()},n.unbindEventListeners=function(){window.removeEventListener("mousemove",n.handleChange),window.removeEventListener("mouseup",n.handleMouseUp)},Ka(n,r)}return Ya(t,e),Va(t,[{key:"componentWillUnmount",value:function(){this.unbindEventListeners()}},{key:"render",value:function(){var e=this,t=this.props.rgb,n=La({default:{alpha:{absolute:"0px 0px 0px 0px",borderRadius:this.props.radius},checkboard:{absolute:"0px 0px 0px 0px",overflow:"hidden",borderRadius:this.props.radius},gradient:{absolute:"0px 0px 0px 0px",background:"linear-gradient(to right, rgba("+t.r+","+t.g+","+t.b+", 0) 0%,\n           rgba("+t.r+","+t.g+","+t.b+", 1) 100%)",boxShadow:this.props.shadow,borderRadius:this.props.radius},container:{position:"relative",height:"100%",margin:"0 3px"},pointer:{position:"absolute",left:100*t.a+"%"},slider:{width:"4px",borderRadius:"1px",height:"8px",boxShadow:"0 0 2px rgba(0, 0, 0, .6)",background:"#fff",marginTop:"1px",transform:"translateX(-2px)"}},vertical:{gradient:{background:"linear-gradient(to bottom, rgba("+t.r+","+t.g+","+t.b+", 0) 0%,\n           rgba("+t.r+","+t.g+","+t.b+", 1) 100%)"},pointer:{left:0,top:100*t.a+"%"}},overwrite:qa({},this.props.style)},{vertical:"vertical"===this.props.direction,overwrite:!0});return r.createElement("div",{style:n.alpha},r.createElement("div",{style:n.checkboard},r.createElement(Wa,{renderers:this.props.renderers})),r.createElement("div",{style:n.gradient}),r.createElement("div",{style:n.container,ref:function(t){return e.container=t},onMouseDown:this.handleMouseDown,onTouchMove:this.handleChange,onTouchStart:this.handleChange},r.createElement("div",{style:n.pointer},this.props.pointer?r.createElement(this.props.pointer,this.props):r.createElement("div",{style:n.slider}))))}}]),t}(t.PureComponent||t.Component),Ja=function(e){function t(e){Xa(this,t);var r=Ka(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return r.handleBlur=function(){r.state.blurValue&&r.setState({value:r.state.blurValue,blurValue:null})},r.handleChange=function(e){r.props.label?r.props.onChange&&r.props.onChange($a({},r.props.label,e.target.value),e):r.props.onChange&&r.props.onChange(e.target.value,e),r.setState({value:e.target.value})},r.handleKeyDown=function(e){var t=String(e.target.value),n=t.indexOf("%")>-1,a=Number(t.replace(/%/g,""));if(!isNaN(a)){var o=r.props.arrowOffset||1;38===e.keyCode&&(null!==r.props.label?r.props.onChange&&r.props.onChange($a({},r.props.label,a+o),e):r.props.onChange&&r.props.onChange(a+o,e),n?r.setState({value:a+o+"%"}):r.setState({value:a+o})),40===e.keyCode&&(null!==r.props.label?r.props.onChange&&r.props.onChange($a({},r.props.label,a-o),e):r.props.onChange&&r.props.onChange(a-o,e),n?r.setState({value:a-o+"%"}):r.setState({value:a-o}))}},r.handleDrag=function(e){if(r.props.dragLabel){var t=Math.round(r.props.value+e.movementX);t>=0&&t<=r.props.dragMax&&r.props.onChange&&r.props.onChange($a({},r.props.label,t),e)}},r.handleMouseDown=function(e){r.props.dragLabel&&(e.preventDefault(),r.handleDrag(e),window.addEventListener("mousemove",r.handleDrag),window.addEventListener("mouseup",r.handleMouseUp))},r.handleMouseUp=function(){r.unbindEventListeners()},r.unbindEventListeners=function(){window.removeEventListener("mousemove",r.handleDrag),window.removeEventListener("mouseup",r.handleMouseUp)},r.state={value:String(e.value).toUpperCase(),blurValue:String(e.value).toUpperCase()},r}return Ya(t,e),Va(t,[{key:"componentWillReceiveProps",value:function(e){var t=this.input;e.value!==this.state.value&&(t===document.activeElement?this.setState({blurValue:String(e.value).toUpperCase()}):this.setState({value:String(e.value).toUpperCase(),blurValue:!this.state.blurValue&&String(e.value).toUpperCase()}))}},{key:"componentWillUnmount",value:function(){this.unbindEventListeners()}},{key:"render",value:function(){var e=this,t=La({default:{wrap:{position:"relative"}},"user-override":{wrap:this.props.style&&this.props.style.wrap?this.props.style.wrap:{},input:this.props.style&&this.props.style.input?this.props.style.input:{},label:this.props.style&&this.props.style.label?this.props.style.label:{}},"dragLabel-true":{label:{cursor:"ew-resize"}}},{"user-override":!0},this.props);return r.createElement("div",{style:t.wrap},r.createElement("input",{style:t.input,ref:function(t){return e.input=t},value:this.state.value,onKeyDown:this.handleKeyDown,onChange:this.handleChange,onBlur:this.handleBlur,placeholder:this.props.placeholder,spellCheck:"false"}),this.props.label&&!this.props.hideLabel?r.createElement("span",{style:t.label,onMouseDown:this.handleMouseDown},this.props.label):null)}}]),t}(t.PureComponent||t.Component),Qa=function(e,t,r,n){e.preventDefault();var a=n.clientWidth,o=n.clientHeight,i="number"==typeof e.pageX?e.pageX:e.touches[0].pageX,l="number"==typeof e.pageY?e.pageY:e.touches[0].pageY,s=i-(n.getBoundingClientRect().left+window.pageXOffset),c=l-(n.getBoundingClientRect().top+window.pageYOffset);if("vertical"===r.direction){var u=void 0;if(c<0)u=359;else if(c>o)u=0;else{u=360*(-100*c/o+100)/100}if(r.hsl.h!==u)return{h:u,s:r.hsl.s,l:r.hsl.l,a:r.hsl.a,source:"rgb"}}else{var p=void 0;if(s<0)p=0;else if(s>a)p=359;else{p=360*(100*s/a)/100}if(r.hsl.h!==p)return{h:p,s:r.hsl.s,l:r.hsl.l,a:r.hsl.a,source:"rgb"}}return null},eo=function(e){function t(){var e,r,n;Xa(this,t);for(var a=arguments.length,o=Array(a),i=0;i<a;i++)o[i]=arguments[i];return r=n=Ka(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(o))),n.handleChange=function(e,t){var r=Qa(e,t,n.props,n.container);r&&n.props.onChange&&n.props.onChange(r,e)},n.handleMouseDown=function(e){n.handleChange(e,!0),window.addEventListener("mousemove",n.handleChange),window.addEventListener("mouseup",n.handleMouseUp)},n.handleMouseUp=function(){n.unbindEventListeners()},Ka(n,r)}return Ya(t,e),Va(t,[{key:"componentWillUnmount",value:function(){this.unbindEventListeners()}},{key:"unbindEventListeners",value:function(){window.removeEventListener("mousemove",this.handleChange),window.removeEventListener("mouseup",this.handleMouseUp)}},{key:"render",value:function(){var e=this,t=this.props.direction,n=void 0===t?"horizontal":t,a=La({default:{hue:{absolute:"0px 0px 0px 0px",borderRadius:this.props.radius,boxShadow:this.props.shadow},container:{padding:"0 2px",position:"relative",height:"100%",borderRadius:this.props.radius},pointer:{position:"absolute",left:100*this.props.hsl.h/360+"%"},slider:{marginTop:"1px",width:"4px",borderRadius:"1px",height:"8px",boxShadow:"0 0 2px rgba(0, 0, 0, .6)",background:"#fff",transform:"translateX(-2px)"}},vertical:{pointer:{left:"0px",top:-100*this.props.hsl.h/360+100+"%"}}},{vertical:"vertical"===n});return r.createElement("div",{style:a.hue},r.createElement("div",{className:"hue-"+n,style:a.container,ref:function(t){return e.container=t},onMouseDown:this.handleMouseDown,onTouchMove:this.handleChange,onTouchStart:this.handleChange},r.createElement("style",null,"\n            .hue-horizontal {\n              background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0\n                33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);\n              background: -webkit-linear-gradient(to right, #f00 0%, #ff0\n                17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);\n            }\n\n            .hue-vertical {\n              background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,\n                #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);\n              background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,\n                #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);\n            }\n          "),r.createElement("div",{style:a.pointer},this.props.pointer?r.createElement(this.props.pointer,this.props):r.createElement("div",{style:a.slider}))))}}]),t}(t.PureComponent||t.Component);function to(e){return function(){return e}}var ro=function(){};ro.thatReturns=to,ro.thatReturnsFalse=to(!1),ro.thatReturnsTrue=to(!0),ro.thatReturnsNull=to(null),ro.thatReturnsThis=function(){return this},ro.thatReturnsArgument=function(e){return e};var no=ro,ao=function(e){};var oo=function(e,t,r,n,a,o,i,l){if(ao(t),!e){var s;if(void 0===t)s=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[r,n,a,o,i,l],u=0;(s=new Error(t.replace(/%s/g,function(){return c[u++]}))).name="Invariant Violation"}throw s.framesToPop=1,s}},io="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",lo=o(function(e){e.exports=function(){function e(e,t,r,n,a,o){o!==io&&oo(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")}function t(){return e}e.isRequired=e;var r={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t};return r.checkPropTypes=no,r.PropTypes=r,r}()}),so=function(e){var t=e.zDepth,n=e.radius,a=e.background,o=e.children,i=La({default:{wrap:{position:"relative",display:"inline-block"},content:{position:"relative"},bg:{absolute:"0px 0px 0px 0px",boxShadow:"0 "+t+"px "+4*t+"px rgba(0,0,0,.24)",borderRadius:n,background:a}},"zDepth-0":{bg:{boxShadow:"none"}},"zDepth-1":{bg:{boxShadow:"0 2px 10px rgba(0,0,0,.12), 0 2px 5px rgba(0,0,0,.16)"}},"zDepth-2":{bg:{boxShadow:"0 6px 20px rgba(0,0,0,.19), 0 8px 17px rgba(0,0,0,.2)"}},"zDepth-3":{bg:{boxShadow:"0 17px 50px rgba(0,0,0,.19), 0 12px 15px rgba(0,0,0,.24)"}},"zDepth-4":{bg:{boxShadow:"0 25px 55px rgba(0,0,0,.21), 0 16px 28px rgba(0,0,0,.22)"}},"zDepth-5":{bg:{boxShadow:"0 40px 77px rgba(0,0,0,.22), 0 27px 24px rgba(0,0,0,.2)"}},square:{bg:{borderRadius:"0"}},circle:{bg:{borderRadius:"50%"}}},{"zDepth-1":1===t});return r.createElement("div",{style:i.wrap},r.createElement("div",{style:i.bg}),r.createElement("div",{style:i.content},o))};so.propTypes={background:lo.string,zDepth:lo.oneOf([0,1,2,3,4,5]),radius:lo.number},so.defaultProps={background:"#fff",zDepth:1,radius:2};var co=function(){return s.Date.now()},uo=NaN,po=/^\s+|\s+$/g,ho=/^[-+]0x[0-9a-f]+$/i,fo=/^0b[01]+$/i,bo=/^0o[0-7]+$/i,go=parseInt;var vo=function(e){if("number"==typeof e)return e;if(Hr(e))return uo;if(ee(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=ee(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(po,"");var r=fo.test(e);return r||bo.test(e)?go(e.slice(2),r?2:8):ho.test(e)?uo:+e},xo="Expected a function",yo=Math.max,mo=Math.min;var wo=function(e,t,r){var n,a,o,i,l,s,c=0,u=!1,p=!1,h=!0;if("function"!=typeof e)throw new TypeError(xo);function f(t){var r=n,o=a;return n=a=void 0,c=t,i=e.apply(o,r)}function d(e){var r=e-s;return void 0===s||r>=t||r<0||p&&e-c>=o}function b(){var e=co();if(d(e))return g(e);l=setTimeout(b,function(e){var r=t-(e-s);return p?mo(r,o-(e-c)):r}(e))}function g(e){return l=void 0,h&&n?f(e):(n=a=void 0,i)}function v(){var e=co(),r=d(e);if(n=arguments,a=this,s=e,r){if(void 0===l)return function(e){return c=e,l=setTimeout(b,t),u?f(e):i}(s);if(p)return l=setTimeout(b,t),f(s)}return void 0===l&&(l=setTimeout(b,t)),i}return t=vo(t)||0,ee(r)&&(u=!!r.leading,o=(p="maxWait"in r)?yo(vo(r.maxWait)||0,t):o,h="trailing"in r?!!r.trailing:h),v.cancel=function(){void 0!==l&&clearTimeout(l),c=0,n=s=a=l=void 0},v.flush=function(){return void 0===l?i:g(co())},v},Eo="Expected a function";var _o=function(e,t,r){var n=!0,a=!0;if("function"!=typeof e)throw new TypeError(Eo);return ee(r)&&(n="leading"in r?!!r.leading:n,a="trailing"in r?!!r.trailing:a),wo(e,t,{leading:n,maxWait:t,trailing:a})},Co=function(e,t,r,n){e.preventDefault();var a=n.getBoundingClientRect(),o=a.width,i=a.height,l="number"==typeof e.pageX?e.pageX:e.touches[0].pageX,s="number"==typeof e.pageY?e.pageY:e.touches[0].pageY,c=l-(n.getBoundingClientRect().left+window.pageXOffset),u=s-(n.getBoundingClientRect().top+window.pageYOffset);c<0?c=0:c>o?c=o:u<0?u=0:u>i&&(u=i);var p=100*c/o,h=-100*u/i+100;return{h:r.hsl.h,s:p,v:h,a:r.hsl.a,source:"rgb"}},So=function(e){function t(e){Xa(this,t);var r=Ka(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return r.handleChange=function(e,t){r.props.onChange&&r.throttle(r.props.onChange,Co(e,t,r.props,r.container),e)},r.handleMouseDown=function(e){r.handleChange(e,!0),window.addEventListener("mousemove",r.handleChange),window.addEventListener("mouseup",r.handleMouseUp)},r.handleMouseUp=function(){r.unbindEventListeners()},r.throttle=_o(function(e,t,r){e(t,r)},50),r}return Ya(t,e),Va(t,[{key:"componentWillUnmount",value:function(){this.unbindEventListeners()}},{key:"unbindEventListeners",value:function(){window.removeEventListener("mousemove",this.handleChange),window.removeEventListener("mouseup",this.handleMouseUp)}},{key:"render",value:function(){var e=this,t=this.props.style||{},n=t.color,a=t.white,o=t.black,i=t.pointer,l=t.circle,s=La({default:{color:{absolute:"0px 0px 0px 0px",background:"hsl("+this.props.hsl.h+",100%, 50%)",borderRadius:this.props.radius},white:{absolute:"0px 0px 0px 0px",borderRadius:this.props.radius},black:{absolute:"0px 0px 0px 0px",boxShadow:this.props.shadow,borderRadius:this.props.radius},pointer:{position:"absolute",top:-100*this.props.hsv.v+100+"%",left:100*this.props.hsv.s+"%",cursor:"default"},circle:{width:"4px",height:"4px",boxShadow:"0 0 0 1.5px #fff, inset 0 0 1px 1px rgba(0,0,0,.3),\n            0 0 1px 2px rgba(0,0,0,.4)",borderRadius:"50%",cursor:"hand",transform:"translate(-2px, -2px)"}},custom:{color:n,white:a,black:o,pointer:i,circle:l}},{custom:!!this.props.style});return r.createElement("div",{style:s.color,ref:function(t){return e.container=t},onMouseDown:this.handleMouseDown,onTouchMove:this.handleChange,onTouchStart:this.handleChange},r.createElement("style",null,"\n          .saturation-white {\n            background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));\n            background: linear-gradient(to right, #fff, rgba(255,255,255,0));\n          }\n          .saturation-black {\n            background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));\n            background: linear-gradient(to top, #000, rgba(0,0,0,0));\n          }\n        "),r.createElement("div",{style:s.white,className:"saturation-white"},r.createElement("div",{style:s.black,className:"saturation-black"}),r.createElement("div",{style:s.pointer},this.props.pointer?r.createElement(this.props.pointer,this.props):r.createElement("div",{style:s.circle}))))}}]),t}(t.PureComponent||t.Component);var ko=function(e,t){return(w(e)?gn:hn)(e,ue(t))},jo=o(function(e){!function(t){var r=/^\s+/,n=/\s+$/,a=0,o=t.round,i=t.min,l=t.max,s=t.random;function c(e,s){if(e=e||"",s=s||{},e instanceof c)return e;if(!(this instanceof c))return new c(e,s);var u=function(e){var a={r:0,g:0,b:0},o=1,s=null,c=null,u=null,p=!1,h=!1;"string"==typeof e&&(e=function(e){e=e.replace(r,"").replace(n,"").toLowerCase();var t,a=!1;if(j[e])e=j[e],a=!0;else if("transparent"==e)return{r:0,g:0,b:0,a:0,format:"name"};if(t=N.rgb.exec(e))return{r:t[1],g:t[2],b:t[3]};if(t=N.rgba.exec(e))return{r:t[1],g:t[2],b:t[3],a:t[4]};if(t=N.hsl.exec(e))return{h:t[1],s:t[2],l:t[3]};if(t=N.hsla.exec(e))return{h:t[1],s:t[2],l:t[3],a:t[4]};if(t=N.hsv.exec(e))return{h:t[1],s:t[2],v:t[3]};if(t=N.hsva.exec(e))return{h:t[1],s:t[2],v:t[3],a:t[4]};if(t=N.hex8.exec(e))return{r:B(t[1]),g:B(t[2]),b:B(t[3]),a:T(t[4]),format:a?"name":"hex8"};if(t=N.hex6.exec(e))return{r:B(t[1]),g:B(t[2]),b:B(t[3]),format:a?"name":"hex"};if(t=N.hex4.exec(e))return{r:B(t[1]+""+t[1]),g:B(t[2]+""+t[2]),b:B(t[3]+""+t[3]),a:T(t[4]+""+t[4]),format:a?"name":"hex8"};if(t=N.hex3.exec(e))return{r:B(t[1]+""+t[1]),g:B(t[2]+""+t[2]),b:B(t[3]+""+t[3]),format:a?"name":"hex"};return!1}(e));"object"==typeof e&&(G(e.r)&&G(e.g)&&G(e.b)?(f=e.r,d=e.g,b=e.b,a={r:255*A(f,255),g:255*A(d,255),b:255*A(b,255)},p=!0,h="%"===String(e.r).substr(-1)?"prgb":"rgb"):G(e.h)&&G(e.s)&&G(e.v)?(s=H(e.s),c=H(e.v),a=function(e,r,n){e=6*A(e,360),r=A(r,100),n=A(n,100);var a=t.floor(e),o=e-a,i=n*(1-r),l=n*(1-o*r),s=n*(1-(1-o)*r),c=a%6;return{r:255*[n,l,i,i,s,n][c],g:255*[s,n,n,l,i,i][c],b:255*[i,i,s,n,n,l][c]}}(e.h,s,c),p=!0,h="hsv"):G(e.h)&&G(e.s)&&G(e.l)&&(s=H(e.s),u=H(e.l),a=function(e,t,r){var n,a,o;function i(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+6*(t-e)*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}if(e=A(e,360),t=A(t,100),r=A(r,100),0===t)n=a=o=r;else{var l=r<.5?r*(1+t):r+t-r*t,s=2*r-l;n=i(s,l,e+1/3),a=i(s,l,e),o=i(s,l,e-1/3)}return{r:255*n,g:255*a,b:255*o}}(e.h,s,u),p=!0,h="hsl"),e.hasOwnProperty("a")&&(o=e.a));var f,d,b;return o=R(o),{ok:p,format:e.format||h,r:i(255,l(a.r,0)),g:i(255,l(a.g,0)),b:i(255,l(a.b,0)),a:o}}(e);this._originalInput=e,this._r=u.r,this._g=u.g,this._b=u.b,this._a=u.a,this._roundA=o(100*this._a)/100,this._format=s.format||u.format,this._gradientType=s.gradientType,this._r<1&&(this._r=o(this._r)),this._g<1&&(this._g=o(this._g)),this._b<1&&(this._b=o(this._b)),this._ok=u.ok,this._tc_id=a++}function u(e,t,r){e=A(e,255),t=A(t,255),r=A(r,255);var n,a,o=l(e,t,r),s=i(e,t,r),c=(o+s)/2;if(o==s)n=a=0;else{var u=o-s;switch(a=c>.5?u/(2-o-s):u/(o+s),o){case e:n=(t-r)/u+(t<r?6:0);break;case t:n=(r-e)/u+2;break;case r:n=(e-t)/u+4}n/=6}return{h:n,s:a,l:c}}function p(e,t,r){e=A(e,255),t=A(t,255),r=A(r,255);var n,a,o=l(e,t,r),s=i(e,t,r),c=o,u=o-s;if(a=0===o?0:u/o,o==s)n=0;else{switch(o){case e:n=(t-r)/u+(t<r?6:0);break;case t:n=(r-e)/u+2;break;case r:n=(e-t)/u+4}n/=6}return{h:n,s:a,v:c}}function h(e,t,r,n){var a=[M(o(e).toString(16)),M(o(t).toString(16)),M(o(r).toString(16))];return n&&a[0].charAt(0)==a[0].charAt(1)&&a[1].charAt(0)==a[1].charAt(1)&&a[2].charAt(0)==a[2].charAt(1)?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0):a.join("")}function f(e,t,r,n){return[M(P(n)),M(o(e).toString(16)),M(o(t).toString(16)),M(o(r).toString(16))].join("")}function d(e,t){t=0===t?0:t||10;var r=c(e).toHsl();return r.s-=t/100,r.s=F(r.s),c(r)}function b(e,t){t=0===t?0:t||10;var r=c(e).toHsl();return r.s+=t/100,r.s=F(r.s),c(r)}function g(e){return c(e).desaturate(100)}function v(e,t){t=0===t?0:t||10;var r=c(e).toHsl();return r.l+=t/100,r.l=F(r.l),c(r)}function x(e,t){t=0===t?0:t||10;var r=c(e).toRgb();return r.r=l(0,i(255,r.r-o(-t/100*255))),r.g=l(0,i(255,r.g-o(-t/100*255))),r.b=l(0,i(255,r.b-o(-t/100*255))),c(r)}function y(e,t){t=0===t?0:t||10;var r=c(e).toHsl();return r.l-=t/100,r.l=F(r.l),c(r)}function m(e,t){var r=c(e).toHsl(),n=(r.h+t)%360;return r.h=n<0?360+n:n,c(r)}function w(e){var t=c(e).toHsl();return t.h=(t.h+180)%360,c(t)}function E(e){var t=c(e).toHsl(),r=t.h;return[c(e),c({h:(r+120)%360,s:t.s,l:t.l}),c({h:(r+240)%360,s:t.s,l:t.l})]}function _(e){var t=c(e).toHsl(),r=t.h;return[c(e),c({h:(r+90)%360,s:t.s,l:t.l}),c({h:(r+180)%360,s:t.s,l:t.l}),c({h:(r+270)%360,s:t.s,l:t.l})]}function C(e){var t=c(e).toHsl(),r=t.h;return[c(e),c({h:(r+72)%360,s:t.s,l:t.l}),c({h:(r+216)%360,s:t.s,l:t.l})]}function S(e,t,r){t=t||6,r=r||30;var n=c(e).toHsl(),a=360/r,o=[c(e)];for(n.h=(n.h-(a*t>>1)+720)%360;--t;)n.h=(n.h+a)%360,o.push(c(n));return o}function k(e,t){t=t||6;for(var r=c(e).toHsv(),n=r.h,a=r.s,o=r.v,i=[],l=1/t;t--;)i.push(c({h:n,s:a,v:o})),o=(o+l)%1;return i}c.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var e=this.toRgb();return(299*e.r+587*e.g+114*e.b)/1e3},getLuminance:function(){var e,r,n,a=this.toRgb();return e=a.r/255,r=a.g/255,n=a.b/255,.2126*(e<=.03928?e/12.92:t.pow((e+.055)/1.055,2.4))+.7152*(r<=.03928?r/12.92:t.pow((r+.055)/1.055,2.4))+.0722*(n<=.03928?n/12.92:t.pow((n+.055)/1.055,2.4))},setAlpha:function(e){return this._a=R(e),this._roundA=o(100*this._a)/100,this},toHsv:function(){var e=p(this._r,this._g,this._b);return{h:360*e.h,s:e.s,v:e.v,a:this._a}},toHsvString:function(){var e=p(this._r,this._g,this._b),t=o(360*e.h),r=o(100*e.s),n=o(100*e.v);return 1==this._a?"hsv("+t+", "+r+"%, "+n+"%)":"hsva("+t+", "+r+"%, "+n+"%, "+this._roundA+")"},toHsl:function(){var e=u(this._r,this._g,this._b);return{h:360*e.h,s:e.s,l:e.l,a:this._a}},toHslString:function(){var e=u(this._r,this._g,this._b),t=o(360*e.h),r=o(100*e.s),n=o(100*e.l);return 1==this._a?"hsl("+t+", "+r+"%, "+n+"%)":"hsla("+t+", "+r+"%, "+n+"%, "+this._roundA+")"},toHex:function(e){return h(this._r,this._g,this._b,e)},toHexString:function(e){return"#"+this.toHex(e)},toHex8:function(e){return function(e,t,r,n,a){var i=[M(o(e).toString(16)),M(o(t).toString(16)),M(o(r).toString(16)),M(P(n))];if(a&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)&&i[3].charAt(0)==i[3].charAt(1))return i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0)+i[3].charAt(0);return i.join("")}(this._r,this._g,this._b,this._a,e)},toHex8String:function(e){return"#"+this.toHex8(e)},toRgb:function(){return{r:o(this._r),g:o(this._g),b:o(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+o(this._r)+", "+o(this._g)+", "+o(this._b)+")":"rgba("+o(this._r)+", "+o(this._g)+", "+o(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:o(100*A(this._r,255))+"%",g:o(100*A(this._g,255))+"%",b:o(100*A(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+o(100*A(this._r,255))+"%, "+o(100*A(this._g,255))+"%, "+o(100*A(this._b,255))+"%)":"rgba("+o(100*A(this._r,255))+"%, "+o(100*A(this._g,255))+"%, "+o(100*A(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(O[h(this._r,this._g,this._b,!0)]||!1)},toFilter:function(e){var t="#"+f(this._r,this._g,this._b,this._a),r=t,n=this._gradientType?"GradientType = 1, ":"";if(e){var a=c(e);r="#"+f(a._r,a._g,a._b,a._a)}return"progid:DXImageTransform.Microsoft.gradient("+n+"startColorstr="+t+",endColorstr="+r+")"},toString:function(e){var t=!!e;e=e||this._format;var r=!1,n=this._a<1&&this._a>=0;return t||!n||"hex"!==e&&"hex6"!==e&&"hex3"!==e&&"hex4"!==e&&"hex8"!==e&&"name"!==e?("rgb"===e&&(r=this.toRgbString()),"prgb"===e&&(r=this.toPercentageRgbString()),"hex"!==e&&"hex6"!==e||(r=this.toHexString()),"hex3"===e&&(r=this.toHexString(!0)),"hex4"===e&&(r=this.toHex8String(!0)),"hex8"===e&&(r=this.toHex8String()),"name"===e&&(r=this.toName()),"hsl"===e&&(r=this.toHslString()),"hsv"===e&&(r=this.toHsvString()),r||this.toHexString()):"name"===e&&0===this._a?this.toName():this.toRgbString()},clone:function(){return c(this.toString())},_applyModification:function(e,t){var r=e.apply(null,[this].concat([].slice.call(t)));return this._r=r._r,this._g=r._g,this._b=r._b,this.setAlpha(r._a),this},lighten:function(){return this._applyModification(v,arguments)},brighten:function(){return this._applyModification(x,arguments)},darken:function(){return this._applyModification(y,arguments)},desaturate:function(){return this._applyModification(d,arguments)},saturate:function(){return this._applyModification(b,arguments)},greyscale:function(){return this._applyModification(g,arguments)},spin:function(){return this._applyModification(m,arguments)},_applyCombination:function(e,t){return e.apply(null,[this].concat([].slice.call(t)))},analogous:function(){return this._applyCombination(S,arguments)},complement:function(){return this._applyCombination(w,arguments)},monochromatic:function(){return this._applyCombination(k,arguments)},splitcomplement:function(){return this._applyCombination(C,arguments)},triad:function(){return this._applyCombination(E,arguments)},tetrad:function(){return this._applyCombination(_,arguments)}},c.fromRatio=function(e,t){if("object"==typeof e){var r={};for(var n in e)e.hasOwnProperty(n)&&(r[n]="a"===n?e[n]:H(e[n]));e=r}return c(e,t)},c.equals=function(e,t){return!(!e||!t)&&c(e).toRgbString()==c(t).toRgbString()},c.random=function(){return c.fromRatio({r:s(),g:s(),b:s()})},c.mix=function(e,t,r){r=0===r?0:r||50;var n=c(e).toRgb(),a=c(t).toRgb(),o=r/100;return c({r:(a.r-n.r)*o+n.r,g:(a.g-n.g)*o+n.g,b:(a.b-n.b)*o+n.b,a:(a.a-n.a)*o+n.a})},c.readability=function(e,r){var n=c(e),a=c(r);return(t.max(n.getLuminance(),a.getLuminance())+.05)/(t.min(n.getLuminance(),a.getLuminance())+.05)},c.isReadable=function(e,t,r){var n,a,o=c.readability(e,t);switch(a=!1,(n=function(e){var t,r;t=((e=e||{level:"AA",size:"small"}).level||"AA").toUpperCase(),r=(e.size||"small").toLowerCase(),"AA"!==t&&"AAA"!==t&&(t="AA");"small"!==r&&"large"!==r&&(r="small");return{level:t,size:r}}(r)).level+n.size){case"AAsmall":case"AAAlarge":a=o>=4.5;break;case"AAlarge":a=o>=3;break;case"AAAsmall":a=o>=7}return a},c.mostReadable=function(e,t,r){var n,a,o,i,l=null,s=0;a=(r=r||{}).includeFallbackColors,o=r.level,i=r.size;for(var u=0;u<t.length;u++)(n=c.readability(e,t[u]))>s&&(s=n,l=c(t[u]));return c.isReadable(e,l,{level:o,size:i})||!a?l:(r.includeFallbackColors=!1,c.mostReadable(e,["#fff","#000"],r))};var j=c.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},O=c.hexNames=function(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(t[e[r]]=r);return t}(j);function R(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function A(e,r){(function(e){return"string"==typeof e&&-1!=e.indexOf(".")&&1===parseFloat(e)})(e)&&(e="100%");var n=function(e){return"string"==typeof e&&-1!=e.indexOf("%")}(e);return e=i(r,l(0,parseFloat(e))),n&&(e=parseInt(e*r,10)/100),t.abs(e-r)<1e-6?1:e%r/parseFloat(r)}function F(e){return i(1,l(0,e))}function B(e){return parseInt(e,16)}function M(e){return 1==e.length?"0"+e:""+e}function H(e){return e<=1&&(e=100*e+"%"),e}function P(e){return t.round(255*parseFloat(e)).toString(16)}function T(e){return B(e)/255}var D,z,L,N=(z="[\\s|\\(]+("+(D="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+D+")[,|\\s]+("+D+")\\s*\\)?",L="[\\s|\\(]+("+D+")[,|\\s]+("+D+")[,|\\s]+("+D+")[,|\\s]+("+D+")\\s*\\)?",{CSS_UNIT:new RegExp(D),rgb:new RegExp("rgb"+z),rgba:new RegExp("rgba"+L),hsl:new RegExp("hsl"+z),hsla:new RegExp("hsla"+L),hsv:new RegExp("hsv"+z),hsva:new RegExp("hsva"+L),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function G(e){return!!N.CSS_UNIT.exec(e)}e.exports?e.exports=c:window.tinycolor=c}(Math)}),Oo={simpleCheckForValidColor:function(e){var t=0,r=0;return ko(["r","g","b","a","h","s","l","v"],function(n){if(e[n]&&(t+=1,isNaN(e[n])||(r+=1),"s"===n||"l"===n)){/^\d+%$/.test(e[n])&&(r+=1)}}),t===r&&e},toState:function(e,t){var r=e.hex?jo(e.hex):jo(e),n=r.toHsl(),a=r.toHsv(),o=r.toRgb(),i=r.toHex();return 0===n.s&&(n.h=t||0,a.h=t||0),{hsl:n,hex:"000000"===i&&0===o.a?"transparent":"#"+i,rgb:o,hsv:a,oldHue:e.h||t||n.h,source:e.source}},isValidHex:function(e){var t="#"===String(e).charAt(0)?1:0;return e.length!==4+t&&e.length<7+t&&jo(e).isValid()},getContrastingColor:function(e){if(!e)return"#fff";var t=this.toState(e);return"transparent"===t.hex?"rgba(0,0,0,0.4)":(299*t.rgb.r+587*t.rgb.g+114*t.rgb.b)/1e3>=128?"#000":"#fff"}},Ro=function(e){var n=function(t){function n(e){Xa(this,n);var t=Ka(this,(n.__proto__||Object.getPrototypeOf(n)).call(this));return t.handleChange=function(e,r){if(Oo.simpleCheckForValidColor(e)){var n=Oo.toState(e,e.h||t.state.oldHue);t.setState(n),t.props.onChangeComplete&&t.debounce(t.props.onChangeComplete,n,r),t.props.onChange&&t.props.onChange(n,r)}},t.handleSwatchHover=function(e,r){if(Oo.simpleCheckForValidColor(e)){var n=Oo.toState(e,e.h||t.state.oldHue);t.setState(n),t.props.onSwatchHover&&t.props.onSwatchHover(n,r)}},t.state=qa({},Oo.toState(e.color,0)),t.debounce=wo(function(e,t,r){e(t,r)},100),t}return Ya(n,t),Va(n,[{key:"componentWillReceiveProps",value:function(e){this.setState(qa({},Oo.toState(e.color,this.state.oldHue)))}},{key:"render",value:function(){var t={};return this.props.onSwatchHover&&(t.onSwatchHover=this.handleSwatchHover),r.createElement(e,qa({},this.props,this.state,{onChange:this.handleChange},t))}}]),n}(t.PureComponent||t.Component);return n.propTypes=qa({},e.propTypes),n.defaultProps=qa({},e.defaultProps,{color:{h:250,s:.5,l:.2,a:1}}),n},Ao=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"span";return function(n){function a(){var e,t,r;Xa(this,a);for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t=r=Ka(this,(e=a.__proto__||Object.getPrototypeOf(a)).call.apply(e,[this].concat(o))),r.state={focus:!1},r.handleFocus=function(){return r.setState({focus:!0})},r.handleBlur=function(){return r.setState({focus:!1})},Ka(r,t)}return Ya(a,n),Va(a,[{key:"render",value:function(){return r.createElement(t,{onFocus:this.handleFocus,onBlur:this.handleBlur},r.createElement(e,qa({},this.props,this.state)))}}]),a}(r.Component)}(function(e){var t=e.color,n=e.style,a=e.onClick,o=void 0===a?function(){}:a,i=e.onHover,l=e.title,s=void 0===l?t:l,c=e.children,u=e.focus,p=e.focusStyle,h="transparent"===t,f=La({default:{swatch:qa({background:t,height:"100%",width:"100%",cursor:"pointer",position:"relative",outline:"none"},n,u?void 0===p?{}:p:{})}}),d={};return i&&(d.onMouseOver=function(e){return i(t,e)}),r.createElement("div",qa({style:f.swatch,onClick:function(e){return o(t,e)},title:s,tabIndex:0,onKeyDown:function(e){return 13===e.keyCode&&o(t,e)}},d),c,h&&r.createElement(Wa,{borderRadius:f.swatch.borderRadius,boxShadow:"inset 0 0 0 1px rgba(0,0,0,0.1)"}))}),Fo=function(e){var t=e.rgb,n=e.hsl,a=e.width,o=e.height,i=e.onChange,l=e.direction,s=e.style,c=e.renderers,u=e.pointer,p=e.className,h=void 0===p?"":p,f=La({default:{picker:{position:"relative",width:a,height:o},alpha:{radius:"2px",style:s}}});return r.createElement("div",{style:f.picker,className:"alpha-picker "+h},r.createElement(Za,qa({},f.alpha,{rgb:t,hsl:n,pointer:u,renderers:c,onChange:i,direction:l})))};Fo.defaultProps={width:"316px",height:"16px",direction:"horizontal",pointer:function(e){var t=e.direction,n=La({default:{picker:{width:"18px",height:"18px",borderRadius:"50%",transform:"translate(-9px, -1px)",backgroundColor:"rgb(248, 248, 248)",boxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.37)"}},vertical:{picker:{transform:"translate(-3px, -9px)"}}},{vertical:"vertical"===t});return r.createElement("div",{style:n.picker})}};var Bo=Ro(Fo),Mo=function(e){var t=e.colors,n=e.onClick,a=e.onSwatchHover,o=La({default:{swatches:{marginRight:"-10px"},swatch:{width:"22px",height:"22px",float:"left",marginRight:"10px",marginBottom:"10px",borderRadius:"4px"},clear:{clear:"both"}}});return r.createElement("div",{style:o.swatches},dn(t,function(e){return r.createElement(Ao,{key:e,color:e,style:o.swatch,onClick:n,onHover:a,focusStyle:{boxShadow:"0 0 4px "+e}})}),r.createElement("div",{style:o.clear}))},Ho=function(e){var t=e.onChange,n=e.onSwatchHover,a=e.hex,o=e.colors,i=e.width,l=e.triangle,s=e.className,c=void 0===s?"":s,u="transparent"===a,p=function(e,r){Oo.isValidHex(e)&&t({hex:e,source:"hex"},r)},h=La({default:{card:{width:i,background:"#fff",boxShadow:"0 1px rgba(0,0,0,.1)",borderRadius:"6px",position:"relative"},head:{height:"110px",background:a,borderRadius:"6px 6px 0 0",display:"flex",alignItems:"center",justifyContent:"center",position:"relative"},body:{padding:"10px"},label:{fontSize:"18px",color:Oo.getContrastingColor(a),position:"relative"},triangle:{width:"0px",height:"0px",borderStyle:"solid",borderWidth:"0 10px 10px 10px",borderColor:"transparent transparent "+a+" transparent",position:"absolute",top:"-10px",left:"50%",marginLeft:"-10px"},input:{width:"100%",fontSize:"12px",color:"#666",border:"0px",outline:"none",height:"22px",boxShadow:"inset 0 0 0 1px #ddd",borderRadius:"4px",padding:"0 7px",boxSizing:"border-box"}},"hide-triangle":{triangle:{display:"none"}}},{"hide-triangle":"hide"===l});return r.createElement("div",{style:h.card,className:"block-picker "+c},r.createElement("div",{style:h.triangle}),r.createElement("div",{style:h.head},u&&r.createElement(Wa,{borderRadius:"6px 6px 0 0"}),r.createElement("div",{style:h.label},a)),r.createElement("div",{style:h.body},r.createElement(Mo,{colors:o,onClick:p,onSwatchHover:n}),r.createElement(Ja,{style:{input:h.input},value:a,onChange:p})))};Ho.propTypes={width:lo.oneOfType([lo.string,lo.number]),colors:lo.arrayOf(lo.string),triangle:lo.oneOf(["top","hide"])},Ho.defaultProps={width:170,colors:["#D9E3F0","#F47373","#697689","#37D67A","#2CCCE4","#555555","#dce775","#ff8a65","#ba68c8"],triangle:"top"};var Po=Ro(Ho),To="#ffcdd2",Do="#e57373",zo="#f44336",Lo="#d32f2f",No="#b71c1c",Go="#f8bbd0",Uo="#f06292",Io="#e91e63",Wo="#c2185b",Xo="#880e4f",Vo="#e1bee7",$o="#ba68c8",qo="#9c27b0",Yo="#7b1fa2",Ko="#4a148c",Zo="#d1c4e9",Jo="#9575cd",Qo="#673ab7",ei="#512da8",ti="#311b92",ri="#c5cae9",ni="#7986cb",ai="#3f51b5",oi="#303f9f",ii="#1a237e",li="#bbdefb",si="#64b5f6",ci="#2196f3",ui="#1976d2",pi="#0d47a1",hi="#b3e5fc",fi="#4fc3f7",di="#03a9f4",bi="#0288d1",gi="#01579b",vi="#b2ebf2",xi="#4dd0e1",yi="#00bcd4",mi="#0097a7",wi="#006064",Ei="#b2dfdb",_i="#4db6ac",Ci="#009688",Si="#00796b",ki="#004d40",ji="#c8e6c9",Oi="#81c784",Ri="#4caf50",Ai="#388e3c",Fi="#dcedc8",Bi="#aed581",Mi="#8bc34a",Hi="#689f38",Pi="#33691e",Ti="#f0f4c3",Di="#dce775",zi="#cddc39",Li="#afb42b",Ni="#827717",Gi="#fff9c4",Ui="#fff176",Ii="#ffeb3b",Wi="#fbc02d",Xi="#f57f17",Vi="#ffecb3",$i="#ffd54f",qi="#ffc107",Yi="#ffa000",Ki="#ff6f00",Zi="#ffe0b2",Ji="#ffb74d",Qi="#ff9800",el="#f57c00",tl="#e65100",rl="#ffccbc",nl="#ff8a65",al="#ff5722",ol="#e64a19",il="#bf360c",ll="#d7ccc8",sl="#a1887f",cl="#795548",ul="#5d4037",pl="#3e2723",hl="#cfd8dc",fl="#90a4ae",dl="#607d8b",bl="#455a64",gl="#263238",vl=function(e){var t=e.color,n=e.onClick,a=e.onSwatchHover,o=e.hover,i=e.active,l=e.circleSize,s=e.circleSpacing,c=La({default:{swatch:{width:l,height:l,marginRight:s,marginBottom:s,transform:"scale(1)",transition:"100ms transform ease"},Swatch:{borderRadius:"50%",background:"transparent",boxShadow:"inset 0 0 0 "+l/2+"px "+t,transition:"100ms box-shadow ease"}},hover:{swatch:{transform:"scale(1.2)"}},active:{Swatch:{boxShadow:"inset 0 0 0 3px "+t}}},{hover:o,active:i});return r.createElement("div",{style:c.swatch},r.createElement(Ao,{style:c.Swatch,color:t,onClick:n,onHover:a,focusStyle:{boxShadow:c.Swatch.boxShadow+", 0 0 5px "+t}}))};vl.defaultProps={circleSize:28,circleSpacing:14};var xl=Na(vl),yl=function(e){var t=e.width,n=e.onChange,a=e.onSwatchHover,o=e.colors,i=e.hex,l=e.circleSize,s=e.circleSpacing,c=e.className,u=void 0===c?"":c,p=La({default:{card:{width:t,display:"flex",flexWrap:"wrap",marginRight:-s,marginBottom:-s}}}),h=function(e,t){return n({hex:e,source:"hex"},t)};return r.createElement("div",{style:p.card,className:"circle-picker "+u},dn(o,function(e){return r.createElement(xl,{key:e,color:e,onClick:h,onSwatchHover:a,active:i===e.toLowerCase(),circleSize:l,circleSpacing:s})}))};yl.propTypes={width:lo.oneOfType([lo.string,lo.number]),circleSize:lo.number,circleSpacing:lo.number},yl.defaultProps={width:252,circleSize:28,circleSpacing:14,colors:[zo,Io,qo,Qo,ai,ci,di,yi,Ci,Ri,Mi,zi,Ii,qi,Qi,al,cl,dl]};var ml=Ro(yl),wl=a(o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n,a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},o=(n=r)&&n.__esModule?n:{default:n};t.default=function(e){var t=e.fill,r=void 0===t?"currentColor":t,n=e.width,i=void 0===n?24:n,l=e.height,s=void 0===l?24:l,c=e.style,u=void 0===c?{}:c,p=function(e,t){var r={};for(var n in e)t.indexOf(n)>=0||Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n]);return r}(e,["fill","width","height","style"]);return o.default.createElement("svg",a({viewBox:"0 0 24 24",style:a({fill:r,width:i,height:s},u)},p),o.default.createElement("path",{d:"M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"}))}})),El=function(e){function t(){var e,r,n;Xa(this,t);for(var a=arguments.length,o=Array(a),i=0;i<a;i++)o[i]=arguments[i];return r=n=Ka(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(o))),n.state={view:""},n.toggleViews=function(){"hex"===n.state.view?n.setState({view:"rgb"}):"rgb"===n.state.view?n.setState({view:"hsl"}):"hsl"===n.state.view&&(1===n.props.hsl.a?n.setState({view:"hex"}):n.setState({view:"rgb"}))},n.handleChange=function(e,t){e.hex?Oo.isValidHex(e.hex)&&n.props.onChange({hex:e.hex,source:"hex"},t):e.r||e.g||e.b?n.props.onChange({r:e.r||n.props.rgb.r,g:e.g||n.props.rgb.g,b:e.b||n.props.rgb.b,source:"rgb"},t):e.a?(e.a<0?e.a=0:e.a>1&&(e.a=1),n.props.onChange({h:n.props.hsl.h,s:n.props.hsl.s,l:n.props.hsl.l,a:Math.round(100*e.a)/100,source:"rgb"},t)):(e.h||e.s||e.l)&&n.props.onChange({h:e.h||n.props.hsl.h,s:Number(e.s&&e.s||n.props.hsl.s),l:Number(e.l&&e.l||n.props.hsl.l),source:"hsl"},t)},n.showHighlight=function(e){e.target.style.background="#eee"},n.hideHighlight=function(e){e.target.style.background="transparent"},Ka(n,r)}return Ya(t,e),Va(t,[{key:"componentDidMount",value:function(){1===this.props.hsl.a&&"hex"!==this.state.view?this.setState({view:"hex"}):"rgb"!==this.state.view&&"hsl"!==this.state.view&&this.setState({view:"rgb"})}},{key:"componentWillReceiveProps",value:function(e){1!==e.hsl.a&&"hex"===this.state.view&&this.setState({view:"rgb"})}},{key:"render",value:function(){var e=this,t=La({default:{wrap:{paddingTop:"16px",display:"flex"},fields:{flex:"1",display:"flex",marginLeft:"-6px"},field:{paddingLeft:"6px",width:"100%"},alpha:{paddingLeft:"6px",width:"100%"},toggle:{width:"32px",textAlign:"right",position:"relative"},icon:{marginRight:"-4px",marginTop:"12px",cursor:"pointer",position:"relative"},iconHighlight:{position:"absolute",width:"24px",height:"28px",background:"#eee",borderRadius:"4px",top:"10px",left:"12px",display:"none"},input:{fontSize:"11px",color:"#333",width:"100%",borderRadius:"2px",border:"none",boxShadow:"inset 0 0 0 1px #dadada",height:"21px",textAlign:"center"},label:{textTransform:"uppercase",fontSize:"11px",lineHeight:"11px",color:"#969696",textAlign:"center",display:"block",marginTop:"12px"},svg:{fill:"#333",width:"24px",height:"24px",border:"1px transparent solid",borderRadius:"5px"}},disableAlpha:{alpha:{display:"none"}}},this.props,this.state),n=void 0;return"hex"===this.state.view?n=r.createElement("div",{style:t.fields,className:"flexbox-fix"},r.createElement("div",{style:t.field},r.createElement(Ja,{style:{input:t.input,label:t.label},label:"hex",value:this.props.hex,onChange:this.handleChange}))):"rgb"===this.state.view?n=r.createElement("div",{style:t.fields,className:"flexbox-fix"},r.createElement("div",{style:t.field},r.createElement(Ja,{style:{input:t.input,label:t.label},label:"r",value:this.props.rgb.r,onChange:this.handleChange})),r.createElement("div",{style:t.field},r.createElement(Ja,{style:{input:t.input,label:t.label},label:"g",value:this.props.rgb.g,onChange:this.handleChange})),r.createElement("div",{style:t.field},r.createElement(Ja,{style:{input:t.input,label:t.label},label:"b",value:this.props.rgb.b,onChange:this.handleChange})),r.createElement("div",{style:t.alpha},r.createElement(Ja,{style:{input:t.input,label:t.label},label:"a",value:this.props.rgb.a,arrowOffset:.01,onChange:this.handleChange}))):"hsl"===this.state.view&&(n=r.createElement("div",{style:t.fields,className:"flexbox-fix"},r.createElement("div",{style:t.field},r.createElement(Ja,{style:{input:t.input,label:t.label},label:"h",value:Math.round(this.props.hsl.h),onChange:this.handleChange})),r.createElement("div",{style:t.field},r.createElement(Ja,{style:{input:t.input,label:t.label},label:"s",value:Math.round(100*this.props.hsl.s)+"%",onChange:this.handleChange})),r.createElement("div",{style:t.field},r.createElement(Ja,{style:{input:t.input,label:t.label},label:"l",value:Math.round(100*this.props.hsl.l)+"%",onChange:this.handleChange})),r.createElement("div",{style:t.alpha},r.createElement(Ja,{style:{input:t.input,label:t.label},label:"a",value:this.props.hsl.a,arrowOffset:.01,onChange:this.handleChange})))),r.createElement("div",{style:t.wrap,className:"flexbox-fix"},n,r.createElement("div",{style:t.toggle},r.createElement("div",{style:t.icon,onClick:this.toggleViews,ref:function(t){return e.icon=t}},r.createElement(wl,{style:t.svg,onMouseOver:this.showHighlight,onMouseEnter:this.showHighlight,onMouseOut:this.hideHighlight}))))}}]),t}(r.Component),_l=function(){var e=La({default:{picker:{width:"12px",height:"12px",borderRadius:"6px",transform:"translate(-6px, -1px)",backgroundColor:"rgb(248, 248, 248)",boxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.37)"}}});return r.createElement("div",{style:e.picker})},Cl=function(){var e=La({default:{picker:{width:"12px",height:"12px",borderRadius:"6px",boxShadow:"inset 0 0 0 1px #fff",transform:"translate(-6px, -6px)"}}});return r.createElement("div",{style:e.picker})},Sl=function(e){var t=e.onChange,n=e.disableAlpha,a=e.rgb,o=e.hsl,i=e.hsv,l=e.hex,s=e.renderers,c=e.className,u=void 0===c?"":c,p=La({default:{picker:{background:"#fff",borderRadius:"2px",boxShadow:"0 0 2px rgba(0,0,0,.3), 0 4px 8px rgba(0,0,0,.3)",boxSizing:"initial",width:"225px",fontFamily:"Menlo"},saturation:{width:"100%",paddingBottom:"55%",position:"relative",borderRadius:"2px 2px 0 0",overflow:"hidden"},Saturation:{radius:"2px 2px 0 0"},body:{padding:"16px 16px 12px"},controls:{display:"flex"},color:{width:"32px"},swatch:{marginTop:"6px",width:"16px",height:"16px",borderRadius:"8px",position:"relative",overflow:"hidden"},active:{absolute:"0px 0px 0px 0px",borderRadius:"8px",boxShadow:"inset 0 0 0 1px rgba(0,0,0,.1)",background:"rgba("+a.r+", "+a.g+", "+a.b+", "+a.a+")",zIndex:"2"},toggles:{flex:"1"},hue:{height:"10px",position:"relative",marginBottom:"8px"},Hue:{radius:"2px"},alpha:{height:"10px",position:"relative"},Alpha:{radius:"2px"}},disableAlpha:{color:{width:"22px"},alpha:{display:"none"},hue:{marginBottom:"0px"},swatch:{width:"10px",height:"10px",marginTop:"0px"}}},{disableAlpha:n});return r.createElement("div",{style:p.picker,className:"chrome-picker "+u},r.createElement("div",{style:p.saturation},r.createElement(So,{style:p.Saturation,hsl:o,hsv:i,pointer:Cl,onChange:t})),r.createElement("div",{style:p.body},r.createElement("div",{style:p.controls,className:"flexbox-fix"},r.createElement("div",{style:p.color},r.createElement("div",{style:p.swatch},r.createElement("div",{style:p.active}),r.createElement(Wa,{renderers:s}))),r.createElement("div",{style:p.toggles},r.createElement("div",{style:p.hue},r.createElement(eo,{style:p.Hue,hsl:o,pointer:_l,onChange:t})),r.createElement("div",{style:p.alpha},r.createElement(Za,{style:p.Alpha,rgb:a,hsl:o,pointer:_l,renderers:s,onChange:t})))),r.createElement(El,{rgb:a,hsl:o,hex:l,onChange:t,disableAlpha:n})))};Sl.propTypes={disableAlpha:lo.bool},Sl.defaultProps={disableAlpha:!1};var kl=Ro(Sl),jl=function(e){var t=e.color,n=e.onClick,a=void 0===n?function(){}:n,o=e.onSwatchHover,i=e.active,l=La({default:{color:{background:t,width:"15px",height:"15px",float:"left",marginRight:"5px",marginBottom:"5px",position:"relative",cursor:"pointer"},dot:{absolute:"5px 5px 5px 5px",background:Oo.getContrastingColor(t),borderRadius:"50%",opacity:"0"}},active:{dot:{opacity:"1"}},"color-#FFFFFF":{color:{boxShadow:"inset 0 0 0 1px #ddd"},dot:{background:"#000"}},transparent:{dot:{background:"#000"}}},{active:i,"color-#FFFFFF":"#FFFFFF"===t,transparent:"transparent"===t});return r.createElement(Ao,{style:l.color,color:t,onClick:a,onHover:o,focusStyle:{boxShadow:"0 0 4px "+t}},r.createElement("div",{style:l.dot}))},Ol=function(e){var t=e.hex,n=e.rgb,a=e.onChange,o=La({default:{fields:{display:"flex",paddingBottom:"6px",paddingRight:"5px",position:"relative"},active:{position:"absolute",top:"6px",left:"5px",height:"9px",width:"9px",background:t},HEXwrap:{flex:"6",position:"relative"},HEXinput:{width:"80%",padding:"0px",paddingLeft:"20%",border:"none",outline:"none",background:"none",fontSize:"12px",color:"#333",height:"16px"},HEXlabel:{display:"none"},RGBwrap:{flex:"3",position:"relative"},RGBinput:{width:"70%",padding:"0px",paddingLeft:"30%",border:"none",outline:"none",background:"none",fontSize:"12px",color:"#333",height:"16px"},RGBlabel:{position:"absolute",top:"3px",left:"0px",lineHeight:"16px",textTransform:"uppercase",fontSize:"12px",color:"#999"}}}),i=function(e,t){e.r||e.g||e.b?a({r:e.r||n.r,g:e.g||n.g,b:e.b||n.b,source:"rgb"},t):a({hex:e.hex,source:"hex"},t)};return r.createElement("div",{style:o.fields,className:"flexbox-fix"},r.createElement("div",{style:o.active}),r.createElement(Ja,{style:{wrap:o.HEXwrap,input:o.HEXinput,label:o.HEXlabel},label:"hex",value:t,onChange:i}),r.createElement(Ja,{style:{wrap:o.RGBwrap,input:o.RGBinput,label:o.RGBlabel},label:"r",value:n.r,onChange:i}),r.createElement(Ja,{style:{wrap:o.RGBwrap,input:o.RGBinput,label:o.RGBlabel},label:"g",value:n.g,onChange:i}),r.createElement(Ja,{style:{wrap:o.RGBwrap,input:o.RGBinput,label:o.RGBlabel},label:"b",value:n.b,onChange:i}))},Rl=function(e){var t=e.onChange,n=e.onSwatchHover,a=e.colors,o=e.hex,i=e.rgb,l=e.className,s=void 0===l?"":l,c=La({default:{Compact:{background:"#f6f6f6",radius:"4px"},compact:{paddingTop:"5px",paddingLeft:"5px",boxSizing:"initial",width:"240px"},clear:{clear:"both"}}}),u=function(e,r){e.hex?Oo.isValidHex(e.hex)&&t({hex:e.hex,source:"hex"},r):t(e,r)};return r.createElement(so,{style:c.Compact},r.createElement("div",{style:c.compact,className:"compact-picker "+s},r.createElement("div",null,dn(a,function(e){return r.createElement(jl,{key:e,color:e,active:e.toLowerCase()===o,onClick:u,onSwatchHover:n})}),r.createElement("div",{style:c.clear})),r.createElement(Ol,{hex:o,rgb:i,onChange:u})))};Rl.propTypes={colors:lo.arrayOf(lo.string)},Rl.defaultProps={colors:["#4D4D4D","#999999","#FFFFFF","#F44E3B","#FE9200","#FCDC00","#DBDF00","#A4DD00","#68CCCA","#73D8FF","#AEA1FF","#FDA1FF","#333333","#808080","#cccccc","#D33115","#E27300","#FCC400","#B0BC00","#68BC00","#16A5A5","#009CE0","#7B64FF","#FA28FF","#000000","#666666","#B3B3B3","#9F0500","#C45100","#FB9E00","#808900","#194D33","#0C797D","#0062B1","#653294","#AB149E"]};var Al=Ro(Rl),Fl=Na(function(e){var t=e.hover,n=e.color,a=e.onClick,o=e.onSwatchHover,i={position:"relative",zIndex:"2",outline:"2px solid #fff",boxShadow:"0 0 5px 2px rgba(0,0,0,0.25)"},l=La({default:{swatch:{width:"25px",height:"25px",fontSize:"0"}},hover:{swatch:i}},{hover:t});return r.createElement("div",{style:l.swatch},r.createElement(Ao,{color:n,onClick:a,onHover:o,focusStyle:i}))}),Bl=function(e){var t=e.width,n=e.colors,a=e.onChange,o=e.onSwatchHover,i=e.triangle,l=e.className,s=void 0===l?"":l,c=La({default:{card:{width:t,background:"#fff",border:"1px solid rgba(0,0,0,0.2)",boxShadow:"0 3px 12px rgba(0,0,0,0.15)",borderRadius:"4px",position:"relative",padding:"5px",display:"flex",flexWrap:"wrap"},triangle:{position:"absolute",border:"7px solid transparent",borderBottomColor:"#fff"},triangleShadow:{position:"absolute",border:"8px solid transparent",borderBottomColor:"rgba(0,0,0,0.15)"}},"hide-triangle":{triangle:{display:"none"},triangleShadow:{display:"none"}},"top-left-triangle":{triangle:{top:"-14px",left:"10px"},triangleShadow:{top:"-16px",left:"9px"}},"top-right-triangle":{triangle:{top:"-14px",right:"10px"},triangleShadow:{top:"-16px",right:"9px"}},"bottom-left-triangle":{triangle:{top:"35px",left:"10px",transform:"rotate(180deg)"},triangleShadow:{top:"37px",left:"9px",transform:"rotate(180deg)"}},"bottom-right-triangle":{triangle:{top:"35px",right:"10px",transform:"rotate(180deg)"},triangleShadow:{top:"37px",right:"9px",transform:"rotate(180deg)"}}},{"hide-triangle":"hide"===i,"top-left-triangle":"top-left"===i,"top-right-triangle":"top-right"===i,"bottom-left-triangle":"bottom-left"==i,"bottom-right-triangle":"bottom-right"===i}),u=function(e,t){return a({hex:e,source:"hex"},t)};return r.createElement("div",{style:c.card,className:"github-picker "+s},r.createElement("div",{style:c.triangleShadow}),r.createElement("div",{style:c.triangle}),dn(n,function(e){return r.createElement(Fl,{color:e,key:e,onClick:u,onSwatchHover:o})}))};Bl.propTypes={width:lo.oneOfType([lo.string,lo.number]),colors:lo.arrayOf(lo.string),triangle:lo.oneOf(["hide","top-left","top-right","bottom-left","bottom-right"])},Bl.defaultProps={width:200,colors:["#B80000","#DB3E00","#FCCB00","#008B02","#006B76","#1273DE","#004DCF","#5300EB","#EB9694","#FAD0C3","#FEF3BD","#C1E1C5","#BEDADC","#C4DEF6","#BED3F3","#D4C4FB"],triangle:"top-left"};var Ml=Ro(Bl),Hl=function(e){var t=e.width,n=e.height,a=e.onChange,o=e.hsl,i=e.direction,l=e.pointer,s=e.className,c=void 0===s?"":s,u=La({default:{picker:{position:"relative",width:t,height:n},hue:{radius:"2px"}}});return r.createElement("div",{style:u.picker,className:"hue-picker "+c},r.createElement(eo,qa({},u.hue,{hsl:o,pointer:l,onChange:function(e){return a({a:1,h:e.h,l:.5,s:1})},direction:i})))};Hl.defaultProps={width:"316px",height:"16px",direction:"horizontal",pointer:function(e){var t=e.direction,n=La({default:{picker:{width:"18px",height:"18px",borderRadius:"50%",transform:"translate(-9px, -1px)",backgroundColor:"rgb(248, 248, 248)",boxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.37)"}},vertical:{picker:{transform:"translate(-3px, -9px)"}}},{vertical:"vertical"===t});return r.createElement("div",{style:n.picker})}};var Pl=Ro(Hl),Tl=Ro(function(e){var t=e.onChange,n=e.hex,a=e.rgb,o=e.className,i=void 0===o?"":o,l=La({default:{material:{width:"98px",height:"98px",padding:"16px",fontFamily:"Roboto"},HEXwrap:{position:"relative"},HEXinput:{width:"100%",marginTop:"12px",fontSize:"15px",color:"#333",padding:"0px",border:"0px",borderBottom:"2px solid "+n,outline:"none",height:"30px"},HEXlabel:{position:"absolute",top:"0px",left:"0px",fontSize:"11px",color:"#999999",textTransform:"capitalize"},Hex:{style:{}},RGBwrap:{position:"relative"},RGBinput:{width:"100%",marginTop:"12px",fontSize:"15px",color:"#333",padding:"0px",border:"0px",borderBottom:"1px solid #eee",outline:"none",height:"30px"},RGBlabel:{position:"absolute",top:"0px",left:"0px",fontSize:"11px",color:"#999999",textTransform:"capitalize"},split:{display:"flex",marginRight:"-10px",paddingTop:"11px"},third:{flex:"1",paddingRight:"10px"}}}),s=function(e,r){e.hex?Oo.isValidHex(e.hex)&&t({hex:e.hex,source:"hex"},r):(e.r||e.g||e.b)&&t({r:e.r||a.r,g:e.g||a.g,b:e.b||a.b,source:"rgb"},r)};return r.createElement(so,null,r.createElement("div",{style:l.material,className:"material-picker "+i},r.createElement(Ja,{style:{wrap:l.HEXwrap,input:l.HEXinput,label:l.HEXlabel},label:"hex",value:n,onChange:s}),r.createElement("div",{style:l.split,className:"flexbox-fix"},r.createElement("div",{style:l.third},r.createElement(Ja,{style:{wrap:l.RGBwrap,input:l.RGBinput,label:l.RGBlabel},label:"r",value:a.r,onChange:s})),r.createElement("div",{style:l.third},r.createElement(Ja,{style:{wrap:l.RGBwrap,input:l.RGBinput,label:l.RGBlabel},label:"g",value:a.g,onChange:s})),r.createElement("div",{style:l.third},r.createElement(Ja,{style:{wrap:l.RGBwrap,input:l.RGBinput,label:l.RGBlabel},label:"b",value:a.b,onChange:s})))))}),Dl=function(e){var t=e.onChange,n=e.rgb,a=e.hsv,o=e.hex,i=La({default:{fields:{paddingTop:"5px",paddingBottom:"9px",width:"80px",position:"relative"},divider:{height:"5px"},RGBwrap:{position:"relative"},RGBinput:{marginLeft:"40%",width:"40%",height:"18px",border:"1px solid #888888",boxShadow:"inset 0 1px 1px rgba(0,0,0,.1), 0 1px 0 0 #ECECEC",marginBottom:"5px",fontSize:"13px",paddingLeft:"3px",marginRight:"10px"},RGBlabel:{left:"0px",width:"34px",textTransform:"uppercase",fontSize:"13px",height:"18px",lineHeight:"22px",position:"absolute"},HEXwrap:{position:"relative"},HEXinput:{marginLeft:"20%",width:"80%",height:"18px",border:"1px solid #888888",boxShadow:"inset 0 1px 1px rgba(0,0,0,.1), 0 1px 0 0 #ECECEC",marginBottom:"6px",fontSize:"13px",paddingLeft:"3px"},HEXlabel:{position:"absolute",top:"0px",left:"0px",width:"14px",textTransform:"uppercase",fontSize:"13px",height:"18px",lineHeight:"22px"},fieldSymbols:{position:"absolute",top:"5px",right:"-7px",fontSize:"13px"},symbol:{height:"20px",lineHeight:"22px",paddingBottom:"7px"}}}),l=function(e,r){e["#"]?Oo.isValidHex(e["#"])&&t({hex:e["#"],source:"hex"},r):e.r||e.g||e.b?t({r:e.r||n.r,g:e.g||n.g,b:e.b||n.b,source:"rgb"},r):(e.h||e.s||e.v)&&t({h:e.h||a.h,s:e.s||a.s,v:e.v||a.v,source:"hsv"},r)};return r.createElement("div",{style:i.fields},r.createElement(Ja,{style:{wrap:i.RGBwrap,input:i.RGBinput,label:i.RGBlabel},label:"h",value:Math.round(a.h),onChange:l}),r.createElement(Ja,{style:{wrap:i.RGBwrap,input:i.RGBinput,label:i.RGBlabel},label:"s",value:Math.round(100*a.s),onChange:l}),r.createElement(Ja,{style:{wrap:i.RGBwrap,input:i.RGBinput,label:i.RGBlabel},label:"v",value:Math.round(100*a.v),onChange:l}),r.createElement("div",{style:i.divider}),r.createElement(Ja,{style:{wrap:i.RGBwrap,input:i.RGBinput,label:i.RGBlabel},label:"r",value:n.r,onChange:l}),r.createElement(Ja,{style:{wrap:i.RGBwrap,input:i.RGBinput,label:i.RGBlabel},label:"g",value:n.g,onChange:l}),r.createElement(Ja,{style:{wrap:i.RGBwrap,input:i.RGBinput,label:i.RGBlabel},label:"b",value:n.b,onChange:l}),r.createElement("div",{style:i.divider}),r.createElement(Ja,{style:{wrap:i.HEXwrap,input:i.HEXinput,label:i.HEXlabel},label:"#",value:o.replace("#",""),onChange:l}),r.createElement("div",{style:i.fieldSymbols},r.createElement("div",{style:i.symbol},"°"),r.createElement("div",{style:i.symbol},"%"),r.createElement("div",{style:i.symbol},"%")))},zl=function(e){var t=e.hsl,n=La({default:{picker:{width:"12px",height:"12px",borderRadius:"6px",boxShadow:"inset 0 0 0 1px #fff",transform:"translate(-6px, -6px)"}},"black-outline":{picker:{boxShadow:"inset 0 0 0 1px #000"}}},{"black-outline":t.l>.5});return r.createElement("div",{style:n.picker})},Ll=function(){var e=La({default:{triangle:{width:0,height:0,borderStyle:"solid",borderWidth:"4px 0 4px 6px",borderColor:"transparent transparent transparent #fff",position:"absolute",top:"1px",left:"1px"},triangleBorder:{width:0,height:0,borderStyle:"solid",borderWidth:"5px 0 5px 8px",borderColor:"transparent transparent transparent #555"},left:{Extend:"triangleBorder",transform:"translate(-13px, -4px)"},leftInside:{Extend:"triangle",transform:"translate(-8px, -5px)"},right:{Extend:"triangleBorder",transform:"translate(20px, -14px) rotate(180deg)"},rightInside:{Extend:"triangle",transform:"translate(-8px, -5px)"}}});return r.createElement("div",{style:e.pointer},r.createElement("div",{style:e.left},r.createElement("div",{style:e.leftInside})),r.createElement("div",{style:e.right},r.createElement("div",{style:e.rightInside})))},Nl=function(e){var t=e.onClick,n=e.label,a=e.children,o=e.active,i=La({default:{button:{backgroundImage:"linear-gradient(-180deg, #FFFFFF 0%, #E6E6E6 100%)",border:"1px solid #878787",borderRadius:"2px",height:"20px",boxShadow:"0 1px 0 0 #EAEAEA",fontSize:"14px",color:"#000",lineHeight:"20px",textAlign:"center",marginBottom:"10px",cursor:"pointer"}},active:{button:{boxShadow:"0 0 0 1px #878787"}}},{active:o});return r.createElement("div",{style:i.button,onClick:t},n||a)},Gl=function(e){var t=e.rgb,n=e.currentColor,a=La({default:{swatches:{border:"1px solid #B3B3B3",borderBottom:"1px solid #F0F0F0",marginBottom:"2px",marginTop:"1px"},new:{height:"34px",background:"rgb("+t.r+","+t.g+", "+t.b+")",boxShadow:"inset 1px 0 0 #000, inset -1px 0 0 #000, inset 0 1px 0 #000"},current:{height:"34px",background:n,boxShadow:"inset 1px 0 0 #000, inset -1px 0 0 #000, inset 0 -1px 0 #000"},label:{fontSize:"14px",color:"#000",textAlign:"center"}}});return r.createElement("div",null,r.createElement("div",{style:a.label},"new"),r.createElement("div",{style:a.swatches},r.createElement("div",{style:a.new}),r.createElement("div",{style:a.current})),r.createElement("div",{style:a.label},"current"))},Ul=function(e){function t(e){Xa(this,t);var r=Ka(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return r.state={currentColor:e.hex},r}return Ya(t,e),Va(t,[{key:"render",value:function(){var e=this.props.className,t=void 0===e?"":e,n=La({default:{picker:{background:"#DCDCDC",borderRadius:"4px",boxShadow:"0 0 0 1px rgba(0,0,0,.25), 0 8px 16px rgba(0,0,0,.15)",boxSizing:"initial",width:"513px"},head:{backgroundImage:"linear-gradient(-180deg, #F0F0F0 0%, #D4D4D4 100%)",borderBottom:"1px solid #B1B1B1",boxShadow:"inset 0 1px 0 0 rgba(255,255,255,.2), inset 0 -1px 0 0 rgba(0,0,0,.02)",height:"23px",lineHeight:"24px",borderRadius:"4px 4px 0 0",fontSize:"13px",color:"#4D4D4D",textAlign:"center"},body:{padding:"15px 15px 0",display:"flex"},saturation:{width:"256px",height:"256px",position:"relative",border:"2px solid #B3B3B3",borderBottom:"2px solid #F0F0F0",overflow:"hidden"},hue:{position:"relative",height:"256px",width:"19px",marginLeft:"10px",border:"2px solid #B3B3B3",borderBottom:"2px solid #F0F0F0"},controls:{width:"180px",marginLeft:"10px"},top:{display:"flex"},previews:{width:"60px"},actions:{flex:"1",marginLeft:"20px"}}});return r.createElement("div",{style:n.picker,className:"photoshop-picker "+t},r.createElement("div",{style:n.head},this.props.header),r.createElement("div",{style:n.body,className:"flexbox-fix"},r.createElement("div",{style:n.saturation},r.createElement(So,{hsl:this.props.hsl,hsv:this.props.hsv,pointer:zl,onChange:this.props.onChange})),r.createElement("div",{style:n.hue},r.createElement(eo,{direction:"vertical",hsl:this.props.hsl,pointer:Ll,onChange:this.props.onChange})),r.createElement("div",{style:n.controls},r.createElement("div",{style:n.top,className:"flexbox-fix"},r.createElement("div",{style:n.previews},r.createElement(Gl,{rgb:this.props.rgb,currentColor:this.state.currentColor})),r.createElement("div",{style:n.actions},r.createElement(Nl,{label:"OK",onClick:this.props.onAccept,active:!0}),r.createElement(Nl,{label:"Cancel",onClick:this.props.onCancel}),r.createElement(Dl,{onChange:this.props.onChange,rgb:this.props.rgb,hsv:this.props.hsv,hex:this.props.hex}))))))}}]),t}(r.Component);Ul.propTypes={header:lo.string},Ul.defaultProps={header:"Color Picker"};var Il=Ro(Ul),Wl=function(e){var t=e.onChange,n=e.rgb,a=e.hsl,o=e.hex,i=e.disableAlpha,l=La({default:{fields:{display:"flex",paddingTop:"4px"},single:{flex:"1",paddingLeft:"6px"},alpha:{flex:"1",paddingLeft:"6px"},double:{flex:"2"},input:{width:"80%",padding:"4px 10% 3px",border:"none",boxShadow:"inset 0 0 0 1px #ccc",fontSize:"11px"},label:{display:"block",textAlign:"center",fontSize:"11px",color:"#222",paddingTop:"3px",paddingBottom:"4px",textTransform:"capitalize"}},disableAlpha:{alpha:{display:"none"}}},{disableAlpha:i}),s=function(e,r){e.hex?Oo.isValidHex(e.hex)&&t({hex:e.hex,source:"hex"},r):e.r||e.g||e.b?t({r:e.r||n.r,g:e.g||n.g,b:e.b||n.b,a:n.a,source:"rgb"},r):e.a&&(e.a<0?e.a=0:e.a>100&&(e.a=100),e.a/=100,t({h:a.h,s:a.s,l:a.l,a:e.a,source:"rgb"},r))};return r.createElement("div",{style:l.fields,className:"flexbox-fix"},r.createElement("div",{style:l.double},r.createElement(Ja,{style:{input:l.input,label:l.label},label:"hex",value:o.replace("#",""),onChange:s})),r.createElement("div",{style:l.single},r.createElement(Ja,{style:{input:l.input,label:l.label},label:"r",value:n.r,onChange:s,dragLabel:"true",dragMax:"255"})),r.createElement("div",{style:l.single},r.createElement(Ja,{style:{input:l.input,label:l.label},label:"g",value:n.g,onChange:s,dragLabel:"true",dragMax:"255"})),r.createElement("div",{style:l.single},r.createElement(Ja,{style:{input:l.input,label:l.label},label:"b",value:n.b,onChange:s,dragLabel:"true",dragMax:"255"})),r.createElement("div",{style:l.alpha},r.createElement(Ja,{style:{input:l.input,label:l.label},label:"a",value:Math.round(100*n.a),onChange:s,dragLabel:"true",dragMax:"100"})))},Xl=function(e){var t=e.colors,n=e.onClick,a=void 0===n?function(){}:n,o=e.onSwatchHover,i=La({default:{colors:{margin:"0 -10px",padding:"10px 0 0 10px",borderTop:"1px solid #eee",display:"flex",flexWrap:"wrap",position:"relative"},swatchWrap:{width:"16px",height:"16px",margin:"0 10px 10px 0"},swatch:{borderRadius:"3px",boxShadow:"inset 0 0 0 1px rgba(0,0,0,.15)"}},"no-presets":{colors:{display:"none"}}},{"no-presets":!t||!t.length}),l=function(e,t){a({hex:e,source:"hex"},t)};return r.createElement("div",{style:i.colors,className:"flexbox-fix"},t.map(function(e){var t="string"==typeof e?{color:e}:e,n=""+t.color+(t.title||"");return r.createElement("div",{key:n,style:i.swatchWrap},r.createElement(Ao,qa({},t,{style:i.swatch,onClick:l,onHover:o,focusStyle:{boxShadow:"inset 0 0 0 1px rgba(0,0,0,.15), 0 0 4px "+t.color}})))}))};Xl.propTypes={colors:lo.arrayOf(lo.oneOfType([lo.string,lo.shape({color:lo.string,title:lo.string})])).isRequired};var Vl=function(e){var t=e.width,n=e.rgb,a=e.hex,o=e.hsv,i=e.hsl,l=e.onChange,s=e.onSwatchHover,c=e.disableAlpha,u=e.presetColors,p=e.renderers,h=e.className,f=void 0===h?"":h,d=La({default:{picker:{width:t,padding:"10px 10px 0",boxSizing:"initial",background:"#fff",borderRadius:"4px",boxShadow:"0 0 0 1px rgba(0,0,0,.15), 0 8px 16px rgba(0,0,0,.15)"},saturation:{width:"100%",paddingBottom:"75%",position:"relative",overflow:"hidden"},Saturation:{radius:"3px",shadow:"inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)"},controls:{display:"flex"},sliders:{padding:"4px 0",flex:"1"},color:{width:"24px",height:"24px",position:"relative",marginTop:"4px",marginLeft:"4px",borderRadius:"3px"},activeColor:{absolute:"0px 0px 0px 0px",borderRadius:"2px",background:"rgba("+n.r+","+n.g+","+n.b+","+n.a+")",boxShadow:"inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)"},hue:{position:"relative",height:"10px",overflow:"hidden"},Hue:{radius:"2px",shadow:"inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)"},alpha:{position:"relative",height:"10px",marginTop:"4px",overflow:"hidden"},Alpha:{radius:"2px",shadow:"inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)"}},disableAlpha:{color:{height:"10px"},hue:{height:"10px"},alpha:{display:"none"}}},{disableAlpha:c});return r.createElement("div",{style:d.picker,className:"sketch-picker "+f},r.createElement("div",{style:d.saturation},r.createElement(So,{style:d.Saturation,hsl:i,hsv:o,onChange:l})),r.createElement("div",{style:d.controls,className:"flexbox-fix"},r.createElement("div",{style:d.sliders},r.createElement("div",{style:d.hue},r.createElement(eo,{style:d.Hue,hsl:i,onChange:l})),r.createElement("div",{style:d.alpha},r.createElement(Za,{style:d.Alpha,rgb:n,hsl:i,renderers:p,onChange:l}))),r.createElement("div",{style:d.color},r.createElement(Wa,null),r.createElement("div",{style:d.activeColor}))),r.createElement(Wl,{rgb:n,hsl:i,hex:a,onChange:l,disableAlpha:c}),r.createElement(Xl,{colors:u,onClick:l,onSwatchHover:s}))};Vl.propTypes={disableAlpha:lo.bool,width:lo.oneOfType([lo.string,lo.number])},Vl.defaultProps={disableAlpha:!1,width:200,presetColors:["#D0021B","#F5A623","#F8E71C","#8B572A","#7ED321","#417505","#BD10E0","#9013FE","#4A90E2","#50E3C2","#B8E986","#000000","#4A4A4A","#9B9B9B","#FFFFFF"]};var $l=Ro(Vl),ql=function(e){var t=e.hsl,n=e.offset,a=e.onClick,o=void 0===a?function(){}:a,i=e.active,l=e.first,s=e.last,c=La({default:{swatch:{height:"12px",background:"hsl("+t.h+", 50%, "+100*n+"%)",cursor:"pointer"}},first:{swatch:{borderRadius:"2px 0 0 2px"}},last:{swatch:{borderRadius:"0 2px 2px 0"}},active:{swatch:{transform:"scaleY(1.8)",borderRadius:"3.6px/2px"}}},{active:i,first:l,last:s});return r.createElement("div",{style:c.swatch,onClick:function(e){return o({h:t.h,s:.5,l:n,source:"hsl"},e)}})},Yl=function(e){var t=e.onClick,n=e.hsl,a=La({default:{swatches:{marginTop:"20px"},swatch:{boxSizing:"border-box",width:"20%",paddingRight:"1px",float:"left"},clear:{clear:"both"}}});return r.createElement("div",{style:a.swatches},r.createElement("div",{style:a.swatch},r.createElement(ql,{hsl:n,offset:".80",active:Math.round(100*n.l)/100==.8&&Math.round(100*n.s)/100==.5,onClick:t,first:!0})),r.createElement("div",{style:a.swatch},r.createElement(ql,{hsl:n,offset:".65",active:Math.round(100*n.l)/100==.65&&Math.round(100*n.s)/100==.5,onClick:t})),r.createElement("div",{style:a.swatch},r.createElement(ql,{hsl:n,offset:".50",active:Math.round(100*n.l)/100==.5&&Math.round(100*n.s)/100==.5,onClick:t})),r.createElement("div",{style:a.swatch},r.createElement(ql,{hsl:n,offset:".35",active:Math.round(100*n.l)/100==.35&&Math.round(100*n.s)/100==.5,onClick:t})),r.createElement("div",{style:a.swatch},r.createElement(ql,{hsl:n,offset:".20",active:Math.round(100*n.l)/100==.2&&Math.round(100*n.s)/100==.5,onClick:t,last:!0})),r.createElement("div",{style:a.clear}))},Kl=function(e){var t=e.hsl,n=e.onChange,a=e.pointer,o=e.className,i=void 0===o?"":o,l=La({default:{hue:{height:"12px",position:"relative"},Hue:{radius:"2px"}}});return r.createElement("div",{className:"slider-picker "+i},r.createElement("div",{style:l.hue},r.createElement(eo,{style:l.Hue,hsl:t,pointer:a,onChange:n})),r.createElement("div",{style:l.swatches},r.createElement(Yl,{hsl:t,onClick:n})))};Kl.defaultProps={pointer:function(){var e=La({default:{picker:{width:"14px",height:"14px",borderRadius:"6px",transform:"translate(-7px, -1px)",backgroundColor:"rgb(248, 248, 248)",boxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.37)"}}});return r.createElement("div",{style:e.picker})}};var Zl=Ro(Kl),Jl=a(o(function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n,a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},o=(n=r)&&n.__esModule?n:{default:n};t.default=function(e){var t=e.fill,r=void 0===t?"currentColor":t,n=e.width,i=void 0===n?24:n,l=e.height,s=void 0===l?24:l,c=e.style,u=void 0===c?{}:c,p=function(e,t){var r={};for(var n in e)t.indexOf(n)>=0||Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n]);return r}(e,["fill","width","height","style"]);return o.default.createElement("svg",a({viewBox:"0 0 24 24",style:a({fill:r,width:i,height:s},u)},p),o.default.createElement("path",{d:"M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"}))}})),Ql=function(e){var t=e.color,n=e.onClick,a=void 0===n?function(){}:n,o=e.onSwatchHover,i=e.first,l=e.last,s=e.active,c=La({default:{color:{width:"40px",height:"24px",cursor:"pointer",background:t,marginBottom:"1px"},check:{color:Oo.getContrastingColor(t),marginLeft:"8px",display:"none"}},first:{color:{overflow:"hidden",borderRadius:"2px 2px 0 0"}},last:{color:{overflow:"hidden",borderRadius:"0 0 2px 2px"}},active:{check:{display:"block"}},"color-#FFFFFF":{color:{boxShadow:"inset 0 0 0 1px #ddd"},check:{color:"#333"}},transparent:{check:{color:"#333"}}},{first:i,last:l,active:s,"color-#FFFFFF":"#FFFFFF"===t,transparent:"transparent"===t});return r.createElement(Ao,{color:t,style:c.color,onClick:a,onHover:o,focusStyle:{boxShadow:"0 0 4px "+t}},r.createElement("div",{style:c.check},r.createElement(Jl,null)))},es=function(e){var t=e.onClick,n=e.onSwatchHover,a=e.group,o=e.active,i=La({default:{group:{paddingBottom:"10px",width:"40px",float:"left",marginRight:"10px"}}});return r.createElement("div",{style:i.group},dn(a,function(e,i){return r.createElement(Ql,{key:e,color:e,active:e.toLowerCase()===o,first:0===i,last:i===a.length-1,onClick:t,onSwatchHover:n})}))},ts=function(e){var t=e.width,n=e.height,a=e.onChange,o=e.onSwatchHover,i=e.colors,l=e.hex,s=e.className,c=void 0===s?"":s,u=La({default:{picker:{width:t,height:n},overflow:{height:n,overflowY:"scroll"},body:{padding:"16px 0 6px 16px"},clear:{clear:"both"}}}),p=function(e,t){Oo.isValidHex(e)&&a({hex:e,source:"hex"},t)};return r.createElement("div",{style:u.picker,className:"swatches-picker "+c},r.createElement(so,null,r.createElement("div",{style:u.overflow},r.createElement("div",{style:u.body},dn(i,function(e){return r.createElement(es,{key:e.toString(),group:e,active:l,onClick:p,onSwatchHover:o})}),r.createElement("div",{style:u.clear})))))};ts.propTypes={width:lo.oneOfType([lo.string,lo.number]),height:lo.oneOfType([lo.string,lo.number]),colors:lo.arrayOf(lo.arrayOf(lo.string))},ts.defaultProps={width:320,height:240,colors:[[No,Lo,zo,Do,To],[Xo,Wo,Io,Uo,Go],[Ko,Yo,qo,$o,Vo],[ti,ei,Qo,Jo,Zo],[ii,oi,ai,ni,ri],[pi,ui,ci,si,li],[gi,bi,di,fi,hi],[wi,mi,yi,xi,vi],[ki,Si,Ci,_i,Ei],["#194D33",Ai,Ri,Oi,ji],[Pi,Hi,Mi,Bi,Fi],[Ni,Li,zi,Di,Ti],[Xi,Wi,Ii,Ui,Gi],[Ki,Yi,qi,$i,Vi],[tl,el,Qi,Ji,Zi],[il,ol,al,nl,rl],[pl,ul,cl,sl,ll],[gl,bl,dl,fl,hl],["#000000","#525252","#969696","#D9D9D9","#FFFFFF"]]};var rs=Ro(ts),ns=function(e){var t=e.onChange,n=e.onSwatchHover,a=e.hex,o=e.colors,i=e.width,l=e.triangle,s=e.className,c=void 0===s?"":s,u=La({default:{card:{width:i,background:"#fff",border:"0 solid rgba(0,0,0,0.25)",boxShadow:"0 1px 4px rgba(0,0,0,0.25)",borderRadius:"4px",position:"relative"},body:{padding:"15px 9px 9px 15px"},label:{fontSize:"18px",color:"#fff"},triangle:{width:"0px",height:"0px",borderStyle:"solid",borderWidth:"0 9px 10px 9px",borderColor:"transparent transparent #fff transparent",position:"absolute"},triangleShadow:{width:"0px",height:"0px",borderStyle:"solid",borderWidth:"0 9px 10px 9px",borderColor:"transparent transparent rgba(0,0,0,.1) transparent",position:"absolute"},hash:{background:"#F0F0F0",height:"30px",width:"30px",borderRadius:"4px 0 0 4px",float:"left",color:"#98A1A4",display:"flex",alignItems:"center",justifyContent:"center"},input:{width:"100px",fontSize:"14px",color:"#666",border:"0px",outline:"none",height:"28px",boxShadow:"inset 0 0 0 1px #F0F0F0",boxSizing:"content-box",borderRadius:"0 4px 4px 0",float:"left",paddingLeft:"8px"},swatch:{width:"30px",height:"30px",float:"left",borderRadius:"4px",margin:"0 6px 6px 0"},clear:{clear:"both"}},"hide-triangle":{triangle:{display:"none"},triangleShadow:{display:"none"}},"top-left-triangle":{triangle:{top:"-10px",left:"12px"},triangleShadow:{top:"-11px",left:"12px"}},"top-right-triangle":{triangle:{top:"-10px",right:"12px"},triangleShadow:{top:"-11px",right:"12px"}}},{"hide-triangle":"hide"===l,"top-left-triangle":"top-left"===l,"top-right-triangle":"top-right"===l}),p=function(e,r){Oo.isValidHex(e)&&t({hex:e,source:"hex"},r)};return r.createElement("div",{style:u.card,className:"twitter-picker "+c},r.createElement("div",{style:u.triangleShadow}),r.createElement("div",{style:u.triangle}),r.createElement("div",{style:u.body},dn(o,function(e,t){return r.createElement(Ao,{key:t,color:e,hex:e,style:u.swatch,onClick:p,onHover:n,focusStyle:{boxShadow:"0 0 4px "+e}})}),r.createElement("div",{style:u.hash},"#"),r.createElement(Ja,{style:{input:u.input},value:a.replace("#",""),onChange:p}),r.createElement("div",{style:u.clear})))};ns.propTypes={width:lo.oneOfType([lo.string,lo.number]),triangle:lo.oneOf(["hide","top-left","top-right"]),colors:lo.arrayOf(lo.string)},ns.defaultProps={width:276,colors:["#FF6900","#FCB900","#7BDCB5","#00D084","#8ED1FC","#0693E3","#ABB8C3","#EB144C","#F78DA7","#9900EF"],triangle:"top-left"};var as=Ro(ns);e.AlphaPicker=Bo,e.BlockPicker=Po,e.CirclePicker=ml,e.ChromePicker=kl,e.CompactPicker=Al,e.GithubPicker=Ml,e.HuePicker=Pl,e.MaterialPicker=Tl,e.PhotoshopPicker=Il,e.SketchPicker=$l,e.SliderPicker=Zl,e.SwatchesPicker=rs,e.TwitterPicker=as,e.CustomPicker=Ro,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=index.min.js.map
