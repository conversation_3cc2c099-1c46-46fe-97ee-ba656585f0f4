pre {
  background: #282a36;
  width: 100%;
  padding: rem(20px) 0;
  color: $accentDark;
  margin: rem(30px) 0;
  font-size: rem(14px);

  @include media(">=sm") {
    font-size: rem(16px);
    padding: rem(40px) 0;
    margin: rem(50px) 0;
  }

  code {
    @include center(rem(800px));
    padding: 0 rem(20px);

    @include media("<sm") {
      overflow-x: scroll;
    }
  }

  span {
    line-height: 1.5rem;
    font-family: "Monaco", "Consolas", "Menlo", monospace;
  }
}

.highlight {
  margin: rem(20px) 0;

  @include media(">=sm") {
    word-wrap: break-word;
    margin: rem(29px) 0;
  }

  .hll {
    background-color: #282a36;
  }

  .c,     // Comment
  .cm,    // Comment.Multiline
  .cp,    // Comment.Preproc
  .c1,    // Comment.Single
  .cs {   // Comment.Special
    color: #6272a4;
  }

  .err {  // Error
    color: #ff5555;
    background-color: #282a36;
  }

  .kc,    // Keyword.Constant
  .kp,    // Keyword.Pseudo
  .kr,    // Keyword.Reserved
  .kt,    // Keyword.Type
  .no {   // Name.Constant
    color: #66d9ef;
  }

  .l,     // Literal 
  .mf,    // Literal.Number.Float
  .mh,    // Literal.Number.Hex
  .mi,    // Literal.Number.Integer
  .mo,    // Literal.Number.Oct
  .se,    // Literal.String.Escape
  .il {   // Literal.Number.Integer.Long
    color: #ae81ff;
  }

  .p,     // Punctuation
  .nx {   // Name.Other
    color: #f7f7f2;
  }

  .ni,    // Name.Entity
  .nn,    // Name.Namespace
  .py,    // Name.Property
  .nv,    // Name.Variable
  .w,     // Text.Whitespace
  .bp,    // Name.Builtin.Pseudo
  .vc,    // Name.Variable.Class
  .vg,    // Name.Variable.Global
  .vi {   // Name.Variable.Instance
    color: #50fa7b;
  }

  .nl {   // Name.Label
    color: #8be9fd;
  }

  .n,     // Name
  .nb,    // Name.Builtin
  .m {    // Literal.Number
    color: #bd93f9;
  }

  .nt,    // Name.Tag
  .k,     // Keyword
  .kn,    // Keyword.Namespace
  .kd,    // Keyword.Declaration
  .o,     // Operator
  .ow {   // Operator.Word
    color: #ff79c6;
  }

  .ge {   // Generic.Emph
    font-style: italic;
  }

  .gs {   // Generic.Strong
    font-weight: bold;
  }

  .ld,     // Literal.Date
  .s,      // Literal.String
  .sb,     // Literal.String.Backtick
  .sc,     // Literal.String.Char
  .sd,     // Literal.String.Doc
  .s2,     // Literal.String.Double
  .sh,     // Literal.String.Heredoc
  .si,     // Literal.String.Interpol
  .sx,     // Literal.String.Other
  .sr,     // Literal.String.Regex
  .s1,     // Literal.String.Single
  .ss,     // Literal.String.Symbol
  .vglnk { // Link
    color: #f1fa8c;
  }

  .na,    // Name.Attribute
  .nc,    // Name.Class
  .nd,    // Name.Decorator
  .ne,    // Name.Exception
  .nf {   // Name.Function
    color: #50fa7b;
  }
}
