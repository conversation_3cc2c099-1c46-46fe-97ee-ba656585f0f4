@mixin mainFont($weight: 700) {
  font-family: "Titillium Web", "Helvetica Neue", Helvetica, sans-serif;
  font-weight: $weight;
  font-style: normal;
}

@mixin size($width: 30, $height: 30) {
  width: #{$width}px;
  height: #{$height}px;
}

@mixin center($max-width: map-get($jeet, 'max-width'), $pad: 0) {
  @include clearfix;
  width: auto;
  max-width: $max-width;
  float: none;
  display: block;
  margin: {
    right: auto;
    left:  auto;
  };
  padding: {
    left:  $pad;
    right: $pad;
  };
}

@mixin align($direction: both) {
  position: absolute;
  transform-style: preserve-3d;

  @if index("horizontal" "h", $direction) {
    left: 50%;
    transform: translateX(-50%);
  } @else if index("vertical" "v", $direction) {
    top: 50%;
    transform: translateY(-50%);
  } @else {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

@mixin clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}
