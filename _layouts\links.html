---
layout: null
---

<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0'>
    <link rel="shortcut icon" href="/assets/img/uploads/me2.jpeg" type="image/webp">
    <link rel="icon" href="/assets/img/uploads/me2.jpeg" type="image/webp">
    <meta name="description" content="All profiles links of Juan Soberanes available here."/>
    <meta property="og:title" content="Juan Soberanes All Links"/>
    <link rel="canonical" href="https://Juan <PERSON>.bio.link"/>
    <meta property="og:url" content="https://Juan Soberanes.bio.link"/>
    <meta property="og:description" content="All profiles links of Juan Soberanes available here."/>
    <meta property="og:image:secure_url" content="/assets/img/uploads/me2.jpeg">
    <meta property="og:image" content="/assets/img/uploads/me2.jpeg">
    <meta property="profile:username" content="<PERSON> So<PERSON>s"/>
    <meta name="twitter:card" content="summary"/>
    <meta name="twitter:title" content="Juan Soberanes All Links"/>
    <meta name="twitter:image" content="/assets/img/uploads/me2.jpeg">
    <meta name="twitter:url" content="https://Juan Soberanes.bio.link"/>
    <link rel="preload" as="image" href="/assets/img/uploads/me2.jpeg" />
    <title>Juan Soberanes All Links</title>
    <link rel="apple-touch-icon" sizes="180x180" href="https://bio.link/apple-touch-icon.png">
    <link rel="manifest" href="https://bio.link/manifest.json">
    <link rel="dns-prefetch" href="https://analytics.bio.link">
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet">

    <style>
        body,html{-ms-scroll-chaining:none;overscroll-behavior:none;margin:0;padding:0}@-webkit-keyframes slidetounlock{0%{background-position:-100px 0}10%{background-position:-100px 0}50%{background-position:100px 0}to{background-position:100px 0}}@keyframes slidetounlock{0%{background-position:-100px 0}10%{background-position:-100px 0}50%{background-position:100px 0}to{background-position:100px 0}}.min-h-full{min-height:100vh}.flex{display:flex}.flex-both-center{display:flex;justify-content:center;align-items:center}.flex-wrap{flex-wrap:wrap}.flex-h-center{display:flex;justify-content:center}.item-center{align-items:center}.flex-dir-c{flex-direction:column}.mx-auto{margin-left:auto;margin-right:auto}.mt-2{margin-top:2px}.mt-4{margin-top:4px}.mt-8{margin-top:8px}.mt-12{margin-top:12px}.mt-16{margin-top:16px}.mt-24{margin-top:24px}.mt-32{margin-top:32px}.mt-48{margin-top:48px}.mt-120{margin-top:120px}.mb-48{margin-bottom:48px}.m-auto{margin:auto}.ml-6{margin-left:6px}.py-10{padding:10px}.ln-h-22{line-height:22px}.ln-h-32{line-height:32px}.text-fs-14{font-size:14px}.text-fs-16{font-size:16px}.text-fs-18{font-size:18px}.text-fs-20{font-size:20px}.text-fs-22{font-size:22px}.font-inter{font-family:Inter,sans-serif}.font-weight-500{font-weight:500}.font-weight-600{font-weight:600}.color-white{color:#fff}.color-gray{color:hsla(0,0%,100%,.9)}.color-dark{color:#222}.color-danger{color:#ff4963}.page-bg{position:fixed;inset:0;z-index:-1;height:100vh;width:100vw}.w-full{width:100%}.w-250{width:250px}.h-150,.h-165{height:150px}.background-overlay{position:fixed;width:100%;height:100%;z-index:0}.page-overlay{position:fixed;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,.1);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);z-index:100}.page-overlay-btn{text-decoration:none;font-family:Inter,sans-serif;background:transparent;padding:10px 30px;border-radius:6px;font-size:1.2em;display:inline-block;cursor:pointer}.page-overlay-title{font-size:30px;font-family:sans-serif}.page-overlay-text{font-size:18px}.page-image{position:fixed;left:0;top:0;width:100vw;height:100vh;-o-object-fit:cover;object-fit:cover}.display-image{width:96px;height:96px;display:block;border-radius:50%;-o-object-fit:cover;object-fit:cover}.text-center{text-align:center}.page-title{margin-bottom:0}.page-bioline{font-weight:500}.page-full-wrap{width:680px;z-index:10;padding-bottom:176px}.page-item-wrap{transition:transform .15s cubic-bezier(.17,.67,.29,2.71) 0s}.page-item-wrap.show-embed{border-radius:30px;transition:unset;transform:unset}.page-item-wrap.show-embed:hover{transform:unset}.page-item-wrap.show-embed:hover .page-item:after,.page-item-wrap.show-embed:hover .page-item:before{-webkit-animation:unset;animation:unset}.show-embed-item{overflow:hidden;transition:all .3s ease-in-out}.page-item-wrap.show-embed .show-embed-item{overflow:visible}.embed-ind-arrow-icon{transform:rotate(-90deg)}.page-item-wrap.show-embed .embed-ind-arrow-icon{transform:rotate(0deg)}.page-item{box-sizing:border-box;position:absolute;left:0;top:0;width:100%;height:100%;z-index:-1}.page-social{display:block;cursor:pointer;margin:0 12px 12px}.page-social svg{width:28px;height:28px}.relative{position:relative}.link-each-image{width:43px;height:43px;position:absolute;left:9px;-o-object-fit:cover;object-fit:cover}.page-logo{position:absolute;bottom:32px;left:calc(50% - 15px)}.page-logo:hover svg .bl-logo-br{opacity:1}.rounded-md{border-radius:8px}.close-embed{width:25px;height:25px;border-radius:50%;background:#fff;opacity:.7}.embed-wrap{width:100%;box-sizing:border-box;padding:8px;height:100%}.embed-ind-arrow{position:absolute;right:24px;height:14px;top:calc(50% - 7px);margin-bottom:16px}.embed-ind-arrow-icon{transition:all .4s ease-in-out}.close-embed:hover{opacity:1}.cursor-pointer{cursor:pointer}.page-item-each{text-decoration:none;overflow:hidden;z-index:10;box-sizing:border-box}.item-title{width:55%;word-break:break-word}.social-icon-anchor{position:absolute;width:100%;height:100%;left:0;top:0}.page-social:hover{transition:all .1s ease-in-out;transform:scale(1.1)}.page-item-title{font-weight:700;margin-bottom:16px}.embed-wrap-inside{background-color:#fff;display:flex;justify-content:center;box-sizing:border-box;padding:10px;height:100%;overflow:hidden}.embed-wrap-inside iframe{width:auto;min-width:500px}.embed-wrap-inside{position:relative}.embed-wrap-inside:after{content:"";position:absolute;height:85%;width:495px;border:10px solid #fff;transition-property:border;transition-duration:.2s;pointer-events:none}.subscribers-img{width:66px;height:66px;border-radius:50%;box-shadow:0 0 10px rgba(0,0,0,.05);position:absolute;top:-33px;left:calc(50% - 33px)}.subsc-count{color:hsla(0,0%,100%,.9);line-height:24px;font-weight:300}.subsc-err{height:40px;transition:all .25s ease-in-out}.w-400{width:400px}.subsc-button{height:40px;padding:0 16px;background:#fff;border:1px solid #fff;border-radius:0 2px 2px 0;line-height:32px;text-transform:capitalize;color:#000;cursor:pointer;justify-content:center;align-items:center;text-decoration:none}.thank-you-btn{border-radius:2px}.dark-btn{background:#222;height:48px;padding:0 24px;color:#fff;border:1.5px solid #222}.subsc-button:focus{outline:none}.subsc-email{background:hsla(0,0%,100%,.1);border:1.5px solid #fff;border-radius:2px 0 0 2px;padding:0 12px;height:40px;font-size:14px;width:calc(100% - 180px);box-sizing:border-box}.dark-input{border:1.5px solid #222;height:48px;width:calc(100% - 100px);font-size:16px;color:#222}.subsc-email::-moz-placeholder{font-family:Inter,sans-serif;font-size:14px;line-height:19px;color:hsla(0,0%,100%,.5)}.subsc-email:-ms-input-placeholder{font-family:Inter,sans-serif;font-size:14px;line-height:19px;color:hsla(0,0%,100%,.5)}.subsc-email::placeholder{font-family:Inter,sans-serif;font-size:14px;line-height:19px;color:hsla(0,0%,100%,.5)}.dark-input::-moz-placeholder{color:#6e6d7a;font-size:16px}.dark-input:-ms-input-placeholder{color:#6e6d7a;font-size:16px}.dark-input::placeholder{color:#6e6d7a;font-size:16px}.subsc-email:focus{outline:none;background:hsla(0,0%,100%,0)}.subscribers-email-wrap.error-wrap .subsc-button,.subscribers-email-wrap.error-wrap .subsc-email{border:1.5px solid #ff4963}.subscribers-email-wrap.error-wrap .subsc-email{border-right:none}.featured-subscribers{position:fixed;left:0;bottom:0;width:100%;background:#000;box-shadow:0 1.60588px 4.41618px rgba(24,39,75,.12),0 2.81029px 12.8471px rgba(24,39,75,.12);z-index:100;box-sizing:border-box;opacity:1;transition:all .27s cubic-bezier(.1,.9,.9,.9);flex-wrap:wrap;display:flex;flex-flow:column;justify-content:center;align-items:center}.featured-subscribers .subsc-count,.featured-subscribers .subsc-svg,.featured-subscribers .subscribers-email-wrap{opacity:1}.featured-subscribers.hide-subscribers .subsc-count,.featured-subscribers.hide-subscribers .subsc-svg,.featured-subscribers.hide-subscribers .subscribers-email-wrap{opacity:0}.featured-subscribers .subscribers-img{opacity:1;transform:scale(1);transition:all .27s cubic-bezier(.1,.9,.9,.9)}.featured-subscribers.hide-subscribers .subscribers-img{opacity:0;transform:scale(.15);transition:all .27s cubic-bezier(.1,.9,.9,.9)}.featured-subscribers .subsc-title{margin-top:32px;transition:all .27s cubic-bezier(.1,.9,.9,.9)}.featured-subscribers.hide-subscribers .show-after-success .thank-you-msg{opacity:1}.featured-subscribers.hide-subscribers .pt-38{padding-top:38px}.featured-subscribers.hide-subscribers .pt-52{padding-top:52px}.featured-subscribers.hide-subscribers .subsc-title{font-size:16px;font-weight:400;transition:all .27s cubic-bezier(.1,.9,.9,.9)}.featured-subscribers.hide-subscribers{transition:all .25s cubic-bezier(.1,.9,.9,.9);height:56px}.featured-subscribers.hide-subscribers .subscribers-btn{transform:rotate(180deg)}.subscribers-btn{position:absolute;right:16px;top:14px;width:30px;height:30px;display:flex;align-items:center;justify-content:center;border-radius:30px;transition:all .2s ease-out}.subscribers-btn svg path{opacity:.5;transition:all 75ms ease}.subscribers-btn:hover svg path{opacity:1;transition:all 75ms ease}.op-0{opacity:0}.hidden{display:none}.campaign-main-wrap{width:400px}.campaign-email{border:1px solid #e7e7e9;width:80px;height:80px;-o-object-fit:cover;object-fit:cover;border-radius:50%}.campaign-subsc-count{color:rgba(34,34,34,.9)}.campaign-user-link{background:rgba(0,0,0,.05);-webkit-backdrop-filter:blur(200px);backdrop-filter:blur(200px);border-radius:68px;height:42px;padding:0 12px;margin:0 auto;text-decoration:none;position:fixed;bottom:36px;transition:all .15s ease-out}.campaign-user-link:hover{background:rgba(0,0,0,.1)}.campaign-user-image{width:24px;height:24px;border-radius:50%;margin-right:12px}.bl-circle-loader{border-right:3px solid transparent;border-top:3px solid transparent;border-radius:50%;border-color:#000 #000 transparent transparent;border-style:solid;border-width:3px;width:15px;height:15px;-webkit-animation:spin 1s linear infinite;animation:spin 1s linear infinite;position:absolute}.dark-btn .bl-circle-loader{border-top:3px solid #fff;border-right:3px solid #fff}@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg)}to{-webkit-transform:rotate(1turn)}}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}@media (max-width:768px){.page-full-wrap{width:90%}.embed-wrap-inside iframe{width:100%;min-width:unset}.page-overlay-title{font-size:24px;margin:16px 0}.embed-wrap-inside:after{width:93%}}@media (max-width:480px){.campaign-main-wrap{width:100%;padding:0 24px}.xs-hidden{display:none}.xs-w-100{width:100%}.xs-w-150{width:150px}.featured-subscribers{height:150px;padding:24px 16px 32px}.h-165{height:165px}.xs-mt-6{margin-top:6px}.xs-mt-8{margin-top:8px}.xs-mt-16{margin-top:16px}.xs-mt-32{margin-top:32px}.xs-mx-24{margin:auto 24px}.xs-block{display:block}.subsc-button{font-weight:400;padding:0 16px;border-radius:0 4px 4px 0}.thank-you-btn{border-radius:4px}.subsc-email{width:calc(100% - 65px);border:1.5px solid #fff;border-radius:4px 0 0 4px}.dark-input{border:1.5px solid #222}.subsc-title{font-size:16px}.featured-subscribers .subsc-title{margin-top:38px;width:calc(100% - 20px)}}@media (max-width:400px){.embed-wrap-inside:after{width:90%}}
    </style>
    

    <style>

        
                    .page-image {
                object-position: center;

                
            }
        
        .display-image {
            border-radius: 50%;
            height: 50%;
            width: 50%

        }

        .page-title {
            font-size: 30px;
            font-weight: 700;
        }

        .page-bioline {
            font-size: 16px;
            font-weight: 600;
        }

        .page-item-title {
            font-size: 16px;
            font-weight: 700;
        }

        .page-item-each {
            color: #1f535c;
            font-family: 'Inter', sans-serif;
            font-size: 16px;
            font-weight: 500;
            text-transform: none;
            border-radius: 8px;
            
                            min-height: 60px;
                    }

        .page-item-wrap {
                            margin: 16px 0;
                    }

        .page-item-wrap:last-child {
            margin-bottom: 0;
        }

                    .page-item-wrap:hover {
                transform: translate3d(0px, 0px, 0px) scale(1.015);
            }
        
         .page-item-wrap{
            
         }

        .page-item {
            border: 0px solid #FFFFFF;
            background: #FFFFFF ;
            border-radius: 8px;
           box-shadow: 0px 6px 14px -6px rgba(24, 39, 75, 0.12), 0px 10px 32px -4px rgba(24, 39, 75, 0.1), inset 0px 0px 2px 1px rgba(24, 39, 75, 0.05);
        }

        
        .embed-wrap iframe, .embed-wrap-inside {
                            border-radius: 8px;
                    }

        .link-each-image, .page-item-wrap {
            border-radius: 8px;
        }

        .page-text-font {
            font-family: 'Inter', sans-serif;
            text-transform: none;
            
        }

        .page-text-color {
            color: #00ff22;
        }

        .social-icon-fill path, .social-icon-fill circle, .social-icon-fill rect {
            fill: #0dff00;
        }

        .page-overlay-btn {
            border: 2px solid #00ff08;
        }

        
    </style>
    <style>
            </style>

</head>

<body>
    <div class="min-h-full flex-h-center" id="background_div">
        <input type="hidden" value="https://bio.link" id="app-url">
                    <input type="hidden" value="null" id="is-featured">
        
                                                <img
                    class="page-image"
                    data-src="/assets/img/man.jpg"
                    alt="background"
                />
                                        <canvas id="bg-canvas" class="background-overlay"></canvas>
                    
                    
            
        <div class="mt-48 page-full-wrap relative ">
        <input type="hidden" value="creator-page" id="page-type">
        <img
            class="display-image m-auto"
            data-src="/assets/img/me2.jpeg"
            alt="Juan Soberanes"
        />
        
        <h2 class="page-title page-text-color page-text-font mt-16 text-center">
            Juan Soberanes
                </h2>
        
                    <div class="flex-both-center flex-wrap mt-24">
                                    <div class="page-social relative">
                        <a class="social-icon-anchor" data-id="linkedin" data-type="social_link" target="_blank" rel="noopener nofollow" href="https://www.linkedin.com/in/juan-s-6b241b173"></a>
                        <svg class="social-icon-fill"width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.25 3C0.25 1.48122 1.48122 0.25 3 0.25H27C28.5188 0.25 29.75 1.48122 29.75 3V27C29.75 28.5188 28.5188 29.75 27 29.75H3C1.48122 29.75 0.25 28.5188 0.25 27V3ZM3 1.75C2.30964 1.75 1.75 2.30964 1.75 3V27C1.75 27.6904 2.30964 28.25 3 28.25H27C27.6904 28.25 28.25 27.6904 28.25 27V3C28.25 2.30964 27.6904 1.75 27 1.75H3ZM10 9.75H8V8.25H10V9.75ZM8.25 22V12H9.75V22H8.25ZM12.25 12H13.75V13.5359C14.5997 12.7384 15.7428 12.25 17 12.25C19.6234 12.25 21.75 14.3766 21.75 17V22H20.25V17C20.25 15.2051 18.7949 13.75 17 13.75C15.2051 13.75 13.75 15.2051 13.75 17V22H12.25V12Z" fill="white"/></svg>
                    </div>
                                    <div class="page-social relative">
                        <a class="social-icon-anchor" data-id="github" data-type="social_link" target="_blank" rel="noopener nofollow" href="https://github.com/cyberducky0o0"></a>
                        <svg class="social-icon-fill" width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19.7998 28.2998C19.3998 28.2998 18.9998 27.9998 18.9998 27.4998V20.1998C18.9998 18.9998 18.9998 18.4998 18.4998 17.9998C18.2998 17.7998 18.1998 17.4998 18.2998 17.1998C18.3998 16.8998 18.5998 16.6998 18.8998 16.6998C22.4998 16.2998 24.6998 15.0998 24.6998 9.99982C24.6998 8.69982 24.1998 7.39982 23.2998 6.49982C23.0998 6.29982 22.9998 5.99982 23.0998 5.69982C23.2998 5.09982 23.3998 4.59982 23.3998 3.99982C23.3998 3.59982 23.2998 3.09982 23.1998 2.69982C22.6998 2.79982 21.5998 2.99982 19.7998 4.19982C19.5998 4.29982 19.3998 4.29982 19.1998 4.29982C16.7998 3.69982 14.1998 3.69982 11.7998 4.29982C11.5998 4.39982 11.3998 4.29982 11.1998 4.19982C9.3998 3.09982 8.2998 2.79982 7.7998 2.79982C7.6998 3.19982 7.5998 3.59982 7.5998 4.09982C7.5998 4.69982 7.6998 5.29982 7.8998 5.79982C7.9998 6.09982 7.8998 6.39982 7.6998 6.59982C7.1998 7.09982 6.8998 7.59982 6.5998 8.19982C6.3998 8.79982 6.1998 9.39982 6.1998 10.0998C6.1998 15.0998 8.3998 16.3998 11.9998 16.7998C12.2998 16.7998 12.4998 16.9998 12.5998 17.2998C12.6998 17.5998 12.5998 17.8998 12.3998 18.0998C11.9998 18.4998 11.7998 19.2998 11.8998 20.4998V22.4998V22.5998V27.5998C11.8998 27.9998 11.5998 28.3998 11.0998 28.3998C10.5998 28.3998 10.2998 28.0998 10.2998 27.5998V23.5998C6.9998 24.1998 5.6998 22.1998 4.8998 20.7998C4.4998 20.0998 4.0998 19.4998 3.6998 19.3998C3.2998 19.2998 3.0998 18.8998 3.1998 18.4998C3.2998 18.0998 3.6998 17.8998 4.0998 17.9998C5.0998 18.2998 5.6998 19.1998 6.1998 20.0998C7.0998 21.4998 7.7998 22.7998 10.3998 22.1998V20.7998C10.2998 19.7998 10.3998 18.9998 10.5998 18.3998C7.4998 17.7998 4.5998 16.1998 4.5998 10.3998C4.5998 9.49982 4.7998 8.69982 5.0998 7.89982C5.4998 6.99982 5.8998 6.39982 6.2998 5.89982C6.1998 5.29982 6.0998 4.69982 6.0998 3.99982C6.0998 3.19982 6.2998 2.39982 6.5998 1.69982C6.6998 1.49982 6.8998 1.29982 7.0998 1.29982C7.3998 1.19982 8.7998 0.999818 11.7998 2.79982C14.2998 2.19982 16.8998 2.19982 19.2998 2.79982C22.2998 0.999818 23.6998 1.19982 23.9998 1.29982C24.1998 1.39982 24.3998 1.49982 24.4998 1.69982C24.7998 2.39982 24.9998 3.19982 24.9998 3.99982C24.9998 4.59982 24.8998 5.29982 24.7998 5.89982C25.7998 7.09982 26.3998 8.49982 26.3998 10.0998C26.3998 15.8998 23.5998 17.5998 20.4998 18.0998C20.6998 18.7998 20.6998 19.4998 20.6998 20.1998V27.4998C20.5998 27.9998 20.1998 28.2998 19.7998 28.2998Z" fill="white"/></svg>
                    </div>
                                    <div class="page-social relative">
                        <a class="social-icon-anchor" data-id="instagram" data-type="social_link" target="_blank" rel="noopener nofollow" href="https://instagram.com/cyber_ducky_"></a>
                        <svg class="social-icon-fill" width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.25 9C0.25 4.16751 4.16751 0.25 9 0.25H21C25.8325 0.25 29.75 4.16751 29.75 9V21C29.75 25.8325 25.8325 29.75 21 29.75H9C4.16751 29.75 0.25 25.8325 0.25 21V9ZM9 1.75C4.99594 1.75 1.75 4.99594 1.75 9V21C1.75 25.0041 4.99594 28.25 9 28.25H21C25.0041 28.25 28.25 25.0041 28.25 21V9C28.25 4.99594 25.0041 1.75 21 1.75H9ZM24 7.75H22V6.25H24V7.75ZM8.25 15C8.25 11.2721 11.2721 8.25 15 8.25C18.7279 8.25 21.75 11.2721 21.75 15C21.75 18.7279 18.7279 21.75 15 21.75C11.2721 21.75 8.25 18.7279 8.25 15ZM15 9.75C12.1005 9.75 9.75 12.1005 9.75 15C9.75 17.8995 12.1005 20.25 15 20.25C17.8995 20.25 20.25 17.8995 20.25 15C20.25 12.1005 17.8995 9.75 15 9.75Z" fill="white"/></svg>
                    </div>
                                    <div class="page-social relative">
                        <a class="social-icon-anchor" data-id="tiktok" data-type="social_link" target="_blank" rel="noopener nofollow" href="https://www.tiktok.com/@cyberduckyofficial"></a>
                        <?xml version="1.0" encoding="utf-8"?><!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
                        <svg fill="#000000" width="800px" height="800px" viewBox="0 0 24 24" id="tiktok" data-name="Line Color" xmlns="http://www.w3.org/2000/svg" class="icon line-color"><path id="primary" d="M21,7H20a4,4,0,0,1-4-4H12V14.5a2.5,2.5,0,1,1-4-2V8.18a6.5,6.5,0,1,0,8,6.32V9.92A8,8,0,0,0,20,11h1Z" style="fill: none; stroke: rgb(38, 255, 0); stroke-linecap: round; stroke-linejoin: round; stroke-width: 2;"></path></svg>                    </div>
                                    <div class="page-social relative">
                        <a class="social-icon-anchor" data-id="youtube" data-type="social_link" target="_blank" rel="noopener nofollow" href="https://youtube.com/@cyber_ducky"></a>
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48" width="48px" height="48px">
                            <g id="surface183812793">
                            <path style=" stroke:none;fill-rule:nonzero;fill:rgb(38, 255, 0);fill-opacity:1;" d="M 12.300781 4 C 12.101562 4 11.957031 4.191406 12.011719 4.378906 L 14.679688 14.015625 L 14.679688 18.324219 C 14.679688 18.601562 14.90625 18.824219 15.179688 18.824219 L 16.585938 18.824219 C 16.863281 18.824219 17.085938 18.601562 17.085938 18.324219 L 17.085938 14.015625 L 19.789062 4.378906 C 19.84375 4.191406 19.699219 4 19.5 4 L 17.878906 4 C 17.644531 4 17.441406 4.160156 17.390625 4.386719 C 17.078125 5.84375 15.992188 10.917969 15.921875 11.398438 L 15.847656 11.398438 C 15.75 10.726562 14.714844 5.820312 14.410156 4.390625 C 14.359375 4.160156 14.160156 4 13.921875 4 Z M 22.796875 7.78125 C 21.980469 7.78125 21.324219 7.941406 20.824219 8.265625 C 20.320312 8.589844 19.953125 9.105469 19.71875 9.8125 C 19.484375 10.519531 19.363281 11.457031 19.363281 12.621094 L 19.363281 14.191406 C 19.363281 15.347656 19.46875 16.273438 19.675781 16.964844 C 19.882812 17.660156 20.230469 18.175781 20.71875 18.503906 C 21.207031 18.832031 21.882812 18.996094 22.742188 19 C 23.578125 19 24.246094 18.839844 24.730469 18.511719 C 25.21875 18.1875 25.574219 17.675781 25.792969 16.976562 C 26.011719 16.273438 26.117188 15.347656 26.117188 14.191406 L 26.117188 12.621094 C 26.117188 11.457031 26.007812 10.523438 25.78125 9.820312 C 25.558594 9.121094 25.207031 8.605469 24.722656 8.273438 C 24.242188 7.945312 23.597656 7.78125 22.796875 7.78125 Z M 27.882812 8 C 27.609375 8 27.382812 8.222656 27.382812 8.5 L 27.382812 16.082031 C 27.382812 17.082031 27.558594 17.824219 27.898438 18.292969 C 28.242188 18.761719 28.769531 18.996094 29.488281 18.996094 C 30.527344 18.996094 31.304688 18.496094 31.824219 17.496094 L 31.875 17.496094 L 32.019531 18.398438 C 32.058594 18.644531 32.269531 18.824219 32.515625 18.824219 L 33.5 18.824219 C 33.777344 18.824219 34 18.597656 34 18.324219 L 34 8.5 C 34 8.222656 33.777344 8 33.5 8 L 32.058594 8 C 31.785156 8 31.558594 8.222656 31.558594 8.5 L 31.558594 16.59375 C 31.464844 16.796875 31.324219 16.960938 31.125 17.089844 C 30.929688 17.222656 30.730469 17.285156 30.515625 17.285156 C 30.269531 17.285156 30.089844 17.183594 29.984375 16.976562 C 29.878906 16.769531 29.824219 16.425781 29.824219 15.945312 L 29.824219 8.5 C 29.824219 8.222656 29.601562 8 29.324219 8 Z M 22.742188 9.460938 C 23.082031 9.460938 23.324219 9.636719 23.457031 9.996094 C 23.59375 10.355469 23.660156 10.921875 23.660156 11.703125 L 23.660156 15.074219 C 23.660156 15.878906 23.59375 16.457031 23.460938 16.808594 C 23.324219 17.160156 23.085938 17.339844 22.742188 17.339844 C 22.402344 17.339844 22.167969 17.160156 22.035156 16.808594 C 21.90625 16.457031 21.84375 15.875 21.84375 15.074219 L 21.84375 11.703125 C 21.84375 10.925781 21.914062 10.355469 22.046875 9.996094 C 22.179688 9.640625 22.414062 9.460938 22.742188 9.460938 Z M 11.5 21 C 8.480469 21 6 23.480469 6 26.5 L 6 38.5 C 6 41.519531 8.480469 44 11.5 44 L 36.5 44 C 39.519531 44 42 41.519531 42 38.5 L 42 26.5 C 42 23.480469 39.519531 21 36.5 21 Z M 11.5 24 L 36.5 24 C 37.898438 24 39 25.101562 39 26.5 L 39 38.5 C 39 39.898438 37.898438 41 36.5 41 L 11.5 41 C 10.101562 41 9 39.898438 9 38.5 L 9 26.5 C 9 25.101562 10.101562 24 11.5 24 Z M 12.5 26 C 12.222656 26 12 26.222656 12 26.5 L 12 27.5 C 12 27.777344 12.222656 28 12.5 28 L 14 28 L 14 37.980469 C 14 38.257812 14.222656 38.480469 14.5 38.480469 L 15.5 38.480469 C 15.777344 38.480469 16 38.257812 16 37.980469 L 16 28 L 17.5 28 C 17.777344 28 18 27.777344 18 27.5 L 18 26.5 C 18 26.222656 17.777344 26 17.5 26 Z M 24.5 26 C 24.222656 26 24 26.222656 24 26.5 L 24 37.980469 C 24 38.257812 24.222656 38.480469 24.5 38.480469 L 25.332031 38.480469 C 25.5625 38.480469 25.765625 38.324219 25.816406 38.097656 L 25.925781 37.648438 L 25.96875 37.648438 C 26.125 37.949219 26.351562 38.183594 26.660156 38.355469 C 26.96875 38.527344 27.308594 38.613281 27.683594 38.613281 C 28.355469 38.613281 28.847656 38.304688 29.164062 37.683594 C 29.480469 37.066406 29.640625 36.097656 29.640625 34.785156 L 29.640625 33.390625 C 29.640625 32.402344 29.578125 31.632812 29.460938 31.074219 C 29.34375 30.519531 29.144531 30.113281 28.878906 29.863281 C 28.613281 29.617188 28.242188 29.488281 27.773438 29.488281 C 27.410156 29.488281 27.070312 29.59375 26.75 29.800781 C 26.4375 30.007812 26.191406 30.273438 26.015625 30.609375 L 26 30.609375 L 26 26.5 C 26 26.222656 25.777344 26 25.5 26 Z M 33.21875 29.496094 C 32.234375 29.496094 31.535156 29.800781 31.128906 30.410156 C 30.722656 31.019531 30.515625 31.988281 30.515625 33.308594 L 30.515625 34.847656 C 30.515625 36.136719 30.710938 37.085938 31.105469 37.699219 C 31.5 38.3125 32.1875 38.617188 33.164062 38.617188 C 33.980469 38.617188 34.609375 38.417969 35.054688 38.015625 C 35.46875 37.644531 35.691406 37.097656 35.722656 36.371094 C 35.722656 36.097656 35.496094 35.871094 35.21875 35.871094 L 34.460938 35.871094 C 34.183594 35.871094 33.953125 36.097656 33.953125 36.371094 C 33.953125 36.558594 33.921875 36.792969 33.855469 36.921875 C 33.734375 37.160156 33.503906 37.265625 33.207031 37.265625 C 32.988281 37.265625 32.824219 37.203125 32.71875 37.074219 C 32.613281 36.945312 32.546875 36.738281 32.511719 36.457031 C 32.476562 36.175781 32.460938 35.75 32.460938 35.183594 L 32.460938 34.734375 L 35.28125 34.734375 C 35.558594 34.734375 35.78125 34.511719 35.78125 34.234375 L 35.78125 33.222656 C 35.78125 32.292969 35.707031 31.5625 35.558594 31.035156 C 35.410156 30.507812 35.152344 30.121094 34.789062 29.871094 C 34.425781 29.621094 33.902344 29.496094 33.21875 29.496094 Z M 21.511719 29.660156 C 21.238281 29.660156 21.011719 29.882812 21.011719 30.160156 L 21.011719 36.667969 C 20.933594 36.832031 20.820312 36.964844 20.660156 37.070312 C 20.503906 37.179688 20.335938 37.230469 20.164062 37.230469 C 19.960938 37.230469 19.816406 37.148438 19.730469 36.980469 C 19.644531 36.816406 19.601562 36.53125 19.601562 36.136719 L 19.601562 30.164062 C 19.601562 29.886719 19.378906 29.664062 19.101562 29.664062 L 18.113281 29.664062 C 17.835938 29.664062 17.613281 29.886719 17.613281 30.164062 L 17.613281 36.253906 C 17.613281 37.066406 17.753906 37.667969 18.03125 38.050781 C 18.308594 38.4375 18.738281 38.628906 19.324219 38.628906 C 20.171875 38.628906 20.800781 38.222656 21.222656 37.402344 L 21.269531 37.402344 L 21.371094 38.0625 C 21.410156 38.304688 21.621094 38.480469 21.867188 38.480469 L 22.5 38.480469 C 22.777344 38.480469 23 38.257812 23 37.980469 L 23 30.160156 C 23 29.882812 22.777344 29.660156 22.5 29.660156 Z M 33.175781 30.835938 C 33.390625 30.835938 33.546875 30.898438 33.644531 31.015625 C 33.746094 31.136719 33.816406 31.339844 33.851562 31.632812 C 33.890625 31.925781 33.90625 32.355469 33.90625 32.921875 L 33.90625 33.554688 L 32.457031 33.554688 L 32.457031 32.921875 C 32.457031 32.347656 32.472656 31.917969 32.507812 31.632812 C 32.539062 31.351562 32.605469 31.148438 32.707031 31.023438 C 32.808594 30.898438 32.964844 30.835938 33.175781 30.835938 Z M 26.878906 30.902344 C 27.082031 30.902344 27.238281 30.984375 27.347656 31.140625 C 27.457031 31.296875 27.535156 31.566406 27.574219 31.9375 C 27.621094 32.3125 27.640625 32.847656 27.640625 33.535156 L 27.640625 34.671875 C 27.640625 35.3125 27.613281 35.820312 27.558594 36.179688 C 27.507812 36.546875 27.421875 36.808594 27.296875 36.960938 C 27.171875 37.113281 27.003906 37.1875 26.792969 37.1875 C 26.628906 37.1875 26.480469 37.148438 26.339844 37.070312 C 26.199219 36.996094 26.085938 36.882812 26 36.726562 L 26 31.71875 C 26.066406 31.484375 26.183594 31.285156 26.347656 31.132812 C 26.511719 30.980469 26.6875 30.902344 26.878906 30.902344 Z M 26.878906 30.902344 "/>
                            </g>
                            </svg>                    </div>
                                    <div class="page-social relative">
                        <a class="social-icon-anchor" data-id="shopify" data-type="social_link" target="_blank" rel="noopener nofollow" href="https://www.cyber-ducky.com/404"></a>
                        <?xml version="1.0" encoding="utf-8"?><!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
                        <svg fill="#000000" width="800px" height="800px" viewBox="-1.5 0 24 24" xmlns="http://www.w3.org/2000/svg"><path style=" stroke:none;fill-rule:nonzero;fill:rgb(38, 255, 0);fill-opacity:1;" d="m13.889 23.979 7.216-1.561s-2.604-17.618-2.625-17.73c-.014-.106-.103-.188-.211-.192-.1 0-1.929-.136-1.929-.136s-1.275-1.274-1.439-1.411c-.034-.031-.074-.056-.118-.073l-.003-.001-.914 21.1h.023zm-3.627-12.674c-.518-.256-1.126-.411-1.77-.424h-.004c-1.447 0-1.5.906-1.5 1.141 0 1.232 3.24 1.715 3.24 4.629 0 2.3-1.44 3.76-3.406 3.76-.04.001-.086.002-.133.002-1.341 0-2.55-.563-3.405-1.465l-.002-.002.646-2.086c.62.544 1.397.923 2.254 1.063l.026.003c.009 0 .02.001.031.001.517 0 .937-.416.944-.932v-.001c0-1.619-2.654-1.694-2.654-4.359-.038-2.235 1.567-4.416 4.823-4.416.062-.003.134-.005.206-.005.605 0 1.179.135 1.693.377l-.024-.01-.945 2.72-.02.01zm-.54-10.475c.151.001.29.051.402.136l-.002-.001c-.979.465-2.059 1.635-2.503 3.992-.656.213-1.293.405-1.889.578.522-1.785 1.773-4.695 3.992-4.695zm1.23 2.949v.135c-.754.232-1.583.484-2.394.736.214-1.287.995-2.357 2.073-2.96l.021-.011c.198.564.313 1.215.313 1.892 0 .073-.001.146-.004.218v-.01zm.539-2.234c.694.074 1.141.867 1.429 1.755-.349.114-.735.231-1.158.366v-.252c.001-.039.002-.085.002-.132 0-.621-.1-1.22-.284-1.779l.011.04zm2.992 1.289c-.02 0-.06.021-.078.021s-.289.075-.714.21c-.423-1.233-1.176-2.37-2.508-2.37h-.115c-.284-.399-.734-.665-1.247-.695h-.004c-3.106 0-4.59 3.877-5.055 5.846-1.194.365-2.063.636-2.16.674-.675.213-.694.232-.772.87-.075.462-1.83 14.063-1.83 14.063l13.561 2.547.927-21.166z"/></svg>                    </div>
                            </div>
        
                    <div class="mt-24">
                        <div class="page-item-wrap relative">
                        <div class="page-item flex-both-center absolute"></div>
                        <a target="_blank" rel="noopener nofollow" class="page-item-each py-10 flex-both-center"
                           href="https://calendly.com/cyberducky"
                           data-id="261652" data-type="page_item">
                                                            <img
                                    class="link-each-image"
                                    data-src="https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fstatic.vecteezy.com%2Fsystem%2Fresources%2Fpreviews%2F003%2F738%2F383%2Foriginal%2Fappointment-date-icon-free-vector.jpg&f=1&nofb=1&ipt=6811869603e9954aa2456935b96e3cc07a6d6857e4428798c23fc75a60b07153&ipo=images"

                                    alt="Schedule a 1 on 1 Mentoring Call"
                                />
                                <span class=" item-title text-center">Schedule a 1 on 1 Mentoring Call</span>
                        </a>
                    </div>
                    <div class="page-item-wrap relative">
                        <div class="page-item flex-both-center absolute"></div>
                        <a target="_blank" rel="noopener nofollow" class="page-item-each py-10 flex-both-center"
                           href="https://discord.gg/zYx48zSnAw"
                           data-id="261711" data-type="page_item">
                                                            <img
                                    class="link-each-image"
                                    data-src="https://assets-global.website-files.com/6257adef93867e50d84d30e2/636e0a6918e57475a843f59f_icon_clyde_black_RGB.svg"
                                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAA7EAAAOxAGVKw4bAAACkklEQVRYhe2X246kMAxEK5AE/v9fewi57sOoLBMSelpa7bwskkWrp8EnZaecMa21hl+8lt9M/h8AAOynD9Ra5bMxBsaYfwOQc0YpBbVWsG+NMViWRWJd178P0FpDSgkpJeScUWsVFTTAuq6XWJafVfcRoLWGGKNESklU4KWTW2svd8KxVKNyPQLEGHGepwSVYBmoAJP2odXoIXifAuScBeA4jgsEVSAAEzrnLtFDaJBHBbT0IQSEEHAcB0IIiDFKLwCQ1Tvn4L2XKKXI908QQ4CcM1JKFwW+vr5ECapABZh82zbZLaUUeO9Ra4VzDrXWG8QQoLWGnPOlBFTg9XqJCqUUUYCr7reqDmstWms3FW4AtVZRoFeBSpzniZzz9wqshfce+75f/EE3m675uq4CMgSgfKWUqRIhBKSUBCDnLMl7Y+p3gVZ6qkCt9Qah1egBmNhaixgjnHPynLUWpZSb9DSqC0BfN8Iw+FKGblrCErx/lu9bluXqpE8A/XefXv179J3X1Ii0VL3dUnYAN9fTNdfbbTY1bwDaMpmcSbz3SCnJVgW+u3rbNtmK2op1Q87mgR0l16vWLrdtmxhKvw23bZMgiLZjDTP1gdGqmXzfd/F/55w44bqul99oCAKMpuNwFwD38UqHY+daa2UiaivWKrEc/VDSCnDBUwC9ej1+ucfZzboxdVLGqDl1Yz4C6ORMxr0+Og9wtT8ZxY/TkO6lTYR9wXL0AP2hpJf8oxMRk+nVM1F/JmQjjs6Es+2nzWhqRByfT1AagIDvVt074qMTcnQSgLWfHcufks+s/fFQqs/5fLFWpQfQ9jtaOQeSvt7+X0Bp+dK+LDqp9v7+MsbINLyclN4BaNea/e3pN/3vAVxK+AfJE4QO4kyDUAAAAABJRU5ErkJggg=="
                                    alt="Discord @CyberDucky World Server"
                                />
                                                        <span class=" item-title text-center">Discord @CyberDucky World Server</span>
                        </a>
                    </div>

                                                                                    <div class="page-item-wrap relative">
                        <div class="page-item flex-both-center absolute"></div>
                        <a target="_blank" rel="noopener nofollow" class="page-item-each py-10 flex-both-center"
                           href="https://cyber-ducky.com/404"
                           data-id="261685" data-type="page_item">
                                                            <img
                                    class="link-each-image"
                                    data-src="https://cdn.bio.link/uploads/thumbnails/2021-12-21/8nya62PrC7GqbJ0MnOKJC4cObliRy9yW.png"
                                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAA7EAAAOxAGVKw4bAAACaElEQVRYhY2X4bKrMAiEMcH2Lfv+r1CN5P44g3ddN7GZyViNwsdC0C6fz6ebmdVazd1tXVdb19Ver5e93+/zd16vtVqt1UoptiyLLctivXfrvdtxHHYch7XWbN/3c27bZt/v17ZtO6+11iwirJiZLctiONKwWvt1zGzieimlGE6+EUfvfXhNrbHDnOmnlGJeaz1PGGIG0nsfrqN66JD9mJm5u6cUl/yOQDDShJhFz45rrRYRloG7u583JQAW2Uhu5VRJjROd572+rut5kjfN6iGdMwCfK4gEyPWIuAKMINjxTAFWAW3iczcAVGGkwK8AMwXyuYj4A8giHFXuyHlEnJWMKuF5KcV67xfnmP9Syh1AwSBE5rCUckKoLckpQOe53nv/D/A0cAcwhOoH7JzTdgIgFTvia2Z2Ro0QaPRJhSGA2uPqiPlVEGowBP72fJglYiB0zilAiJkK6RhtuMqfco4K4DMZCe6KX0bacK5yBcMNREFzjn8dzo55r0fELT0caRZUqsDdU41ccyV3GsPjU+tFFfAt+TQ9iwEj5smFqHo6qoCv6Ce7EuA4jssxb06H3CkTAlVA0CnAcRySND8w8xwHbjlVlCw/B/YIwAqw5Cw9fh1ja0YAtIl2vbU2VIBrQMmPTjAFKihWNSLGACzVTH5WALcaO8c5VUD1AIxebVu8x8xu0bfWbkp4a00WjNp+qvpHr9uR/AgREeb7vt8KRjUgllpNLkBVfAmQf808c6G6IEfPX0E8cagtrVS41IDKqZKf35BKfpVShoiIv07IxrjylWEFrVrwCCSPl1Y8k1TBqGszhZQy/wA13RV4q0ZN1QAAAABJRU5ErkJggg=="
                                    alt="GitHub @cyberducky0o0"
                                />
                                                        <span class=" item-title text-center">Merchandise Coming Soon!</span>
                        </a>
                    </div>
                                                                                    <div class="page-item-wrap relative">
                        <div class="page-item flex-both-center absolute"></div>
                        <a target="_blank" rel="noopener nofollow" class="page-item-each py-10 flex-both-center"
                           href="https://cyberducky.medium.com/"
                           data-id="261711" data-type="page_item">
                                                            <img
                                    class="link-each-image"
                                    data-src="https://cdn.bio.link/uploads/thumbnails/2021-12-21/B7oI0PQkOCCfWBIBgfHk5Srft4zNeQvM.png"
                                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAA7EAAAOxAGVKw4bAAACkklEQVRYhe2X246kMAxEK5AE/v9fewi57sOoLBMSelpa7bwskkWrp8EnZaecMa21hl+8lt9M/h8AAOynD9Ra5bMxBsaYfwOQc0YpBbVWsG+NMViWRWJd178P0FpDSgkpJeScUWsVFTTAuq6XWJafVfcRoLWGGKNESklU4KWTW2svd8KxVKNyPQLEGHGepwSVYBmoAJP2odXoIXifAuScBeA4jgsEVSAAEzrnLtFDaJBHBbT0IQSEEHAcB0IIiDFKLwCQ1Tvn4L2XKKXI908QQ4CcM1JKFwW+vr5ECapABZh82zbZLaUUeO9Ra4VzDrXWG8QQoLWGnPOlBFTg9XqJCqUUUYCr7reqDmstWms3FW4AtVZRoFeBSpzniZzz9wqshfce+75f/EE3m675uq4CMgSgfKWUqRIhBKSUBCDnLMl7Y+p3gVZ6qkCt9Qah1egBmNhaixgjnHPynLUWpZSb9DSqC0BfN8Iw+FKGblrCErx/lu9bluXqpE8A/XefXv179J3X1Ii0VL3dUnYAN9fTNdfbbTY1bwDaMpmcSbz3SCnJVgW+u3rbNtmK2op1Q87mgR0l16vWLrdtmxhKvw23bZMgiLZjDTP1gdGqmXzfd/F/55w44bqul99oCAKMpuNwFwD38UqHY+daa2UiaivWKrEc/VDSCnDBUwC9ej1+ucfZzboxdVLGqDl1Yz4C6ORMxr0+Og9wtT8ZxY/TkO6lTYR9wXL0AP2hpJf8oxMRk+nVM1F/JmQjjs6Es+2nzWhqRByfT1AagIDvVt074qMTcnQSgLWfHcufks+s/fFQqs/5fLFWpQfQ9jtaOQeSvt7+X0Bp+dK+LDqp9v7+MsbINLyclN4BaNea/e3pN/3vAVxK+AfJE4QO4kyDUAAAAABJRU5ErkJggg=="
                                    alt="Medium @CyberDucky"
                                />
                                                        <span class=" item-title text-center">Medium @CyberDucky</span>
                        </a>
                    </div>
                    </div>

                </div>
            </div>

        </div>
</body>

    
<script>
    (()=>{var e,t={757:(e,t,r)=>{e.exports=r(666)},80:(e,t,r)=>{"use strict";var n=r(757),o=r.n(n);function a(e,t,r,n,o,a,i){try{var s=e[a](i),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,o)}function i(e){var t=e.target.closest(".page-item-wrap"),r=t.querySelector(".show-embed-item"),n=t.querySelector(".embed-iframe"),o=parseInt(r.getAttribute("data-height")),a=0;r.getAttribute("data-type")&&(a=20),t.classList.toggle("show-embed"),n.src=n.getAttribute("data-src"),n.style.cssText="height: ".concat(0==n.offsetHeight?o-a:0,"px"),r.style.cssText="height: ".concat(0==r.offsetHeight?o+16:"0","px")}function s(e){for(var t=e+"=",r=document.cookie.split(";"),n=0;n<r.length;n++){for(var o=r[n];" "===o.charAt(0);)o=o.substring(1,o.length);if(0===o.indexOf(t))return o.substring(t.length,o.length)}return null}function c(e){if(s(e)&&(t=e,r=location.hostname.split(".").reverse(),n=r[1]+"."+r[0],document.cookie=t+"=; domain="+n+"; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;",!s(e))){try{location.reload(!0)}catch(e){}location.reload()}var t,r,n}var u=document.getElementById("app-url").value,l=document.getElementById("is-featured").value,d=window.location.search,h=new URLSearchParams(d).get("preview"),f=!1;function m(){var e=document.getElementById("subscribe-btn"),t=document.getElementById("subsc-email").value,r=e.getAttribute("data-pageID"),n=e.getAttribute("data-campId");if(""!=t){document.getElementById("btn-text").classList.toggle("op-0"),document.getElementById("btn-loader").classList.toggle("op-0"),document.getElementById("subsc-email-error").innerText="",document.getElementById("subscribers-email-wrap").classList.remove("error-wrap");var o=new FormData;o.append("email",t);var a=decodeURIComponent(s("XSRF-TOKEN")),i=new XMLHttpRequest;i.withCredentials=!0,i.onreadystatechange=function(){if(4===i.readyState&&200===i.status){document.getElementById("btn-text").classList.toggle("op-0"),document.getElementById("btn-loader").classList.toggle("op-0");var e=document.getElementById("subsc-email").value;document.getElementById("thank_you_msg").innerHTML="You’re subscribed as "+e,document.getElementById("subsc-email").value="";for(var t=document.getElementsByClassName("hide-after-success"),r=0;r<t.length;r++)t[r].style.display="none";f=!0,document.getElementsByClassName("show-after-success")[0].classList.remove("hidden"),setTimeout((function(){document.getElementsByClassName("thank-you-title")[0].classList.toggle("hidden"),document.getElementById("featured-subscribers").style.cursor="pointer",document.getElementById("featured-subscribers").classList.toggle("hide-subscribers")}),3e3)}else""!=i.responseText&&JSON.parse(i.responseText).errors&&p(JSON.parse(i.responseText).errors.email[0]),f=!1},i.open("post","".concat(u,"/api/pages/").concat(r,"/campaign/").concat(n,"/subscriber")),i.setRequestHeader("X-XSRF-TOKEN",a),i.send(o)}else p("Please enter a valid email.")}function g(){document.getElementById("subscribe-btn").addEventListener("click",function(){var e,t=(e=o().mark((function e(t){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:m();case 1:case"end":return e.stop()}}),e)})),function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function s(e){a(i,n,o,s,c,"next",e)}function c(e){a(i,n,o,s,c,"throw",e)}s(void 0)}))});return function(e){return t.apply(this,arguments)}}())}function p(e){document.getElementById("subscribers-email-wrap").classList.add("error-wrap"),document.getElementById("subsc-email-error").innerText=e,document.getElementById("btn-text").classList.remove("op-0"),document.getElementById("btn-loader").classList.add("op-0")}function y(){document.getElementById("subscribers-email-wrap").classList.remove("error-wrap"),document.getElementById("subsc-email-error").innerText=""}window.onload=function(){!function(){for(var e=document.getElementsByTagName("img"),t=0;t<e.length;t++){var r=e[t];r.getAttribute("data-src")&&r.setAttribute("src",r.getAttribute("data-src"))}}(),function(){for(var e=document.getElementsByTagName("a"),t=0;t<e.length;t++)e[t].addEventListener("click",(function(e){var t=e.currentTarget,r=["trackEvent",t.getAttribute("data-type"),"Click",t.getAttribute("data-id"),1];try{_paq.push(r)}catch(e){}}))}(),function(){for(var e=document.getElementsByClassName("show-embed"),t=0;t<e.length;t++)e[t].addEventListener("click",i)}(),function(){try{new URLSearchParams(window.location.search).get("preview")?c("page_has_updated_preview"):c("page_has_updated")}catch(e){}}();var e,t=document.getElementById("page-type").value;null==h&&"true"==l&&("creator-page"==t&&(document.getElementById("toggle-subscription-btn").addEventListener("click",(function(e){document.getElementById("featured-subscribers").classList.toggle("hide-subscribers"),f&&document.getElementsByClassName("thank-you-title")[0].classList.toggle("hidden"),document.getElementById("featured-subscribers").style.cursor="default"})),"true"===l&&document.querySelector("#featured-subscribers")&&document.querySelector("#featured-subscribers").addEventListener("click",(function(e){document.getElementById("featured-subscribers").classList.contains("hide-subscribers")&&"toggle-subscription-btn"!==e.target.id&&"sub-toggle"!==e.target.id?(document.getElementById("featured-subscribers").classList.toggle("hide-subscribers"),f&&document.getElementsByClassName("thank-you-title")[0].classList.toggle("hidden"),document.getElementById("featured-subscribers").style.cursor="default"):document.getElementById("featured-subscribers").classList.contains("hide-subscribers")&&(document.getElementById("featured-subscribers").style.cursor="pointer")}))),document.getElementById("subsc-email").addEventListener("keyup",(function(e){"Enter"===e.key&&m()})),g(),(e=new XMLHttpRequest).open("GET","".concat(u,"/sanctum/csrf-cookie"),!0),e.withCredentials=!0,e.send(null),document.getElementById("subsc-email").addEventListener("input",y))}},662:()=>{},328:()=>{},666:e=>{var t=function(e){"use strict";var t,r=Object.prototype,n=r.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var o=t&&t.prototype instanceof p?t:p,a=Object.create(o.prototype),i=new _(n||[]);return a._invoke=function(e,t,r){var n=d;return function(o,a){if(n===f)throw new Error("Generator is already running");if(n===m){if("throw"===o)throw a;return N()}for(r.method=o,r.arg=a;;){var i=r.delegate;if(i){var s=I(i,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===d)throw n=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=f;var c=l(e,t,r);if("normal"===c.type){if(n=r.done?m:h,c.arg===g)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n=m,r.method="throw",r.arg=c.arg)}}}(e,r,i),a}function l(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var d="suspendedStart",h="suspendedYield",f="executing",m="completed",g={};function p(){}function y(){}function v(){}var b={};b[a]=function(){return this};var w=Object.getPrototypeOf,E=w&&w(w(T([])));E&&E!==r&&n.call(E,a)&&(b=E);var L=v.prototype=p.prototype=Object.create(b);function x(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function B(e,t){function r(o,a,i,s){var c=l(e[o],e,a);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==typeof d&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,i,s)}),(function(e){r("throw",e,i,s)})):t.resolve(d).then((function(e){u.value=e,i(u)}),(function(e){return r("throw",e,i,s)}))}s(c.arg)}var o;this._invoke=function(e,n){function a(){return new t((function(t,o){r(e,n,t,o)}))}return o=o?o.then(a,a):a()}}function I(e,r){var n=e.iterator[r.method];if(n===t){if(r.delegate=null,"throw"===r.method){if(e.iterator.return&&(r.method="return",r.arg=t,I(e,r),"throw"===r.method))return g;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return g}var o=l(n,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,g;var a=o.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function _(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function T(e){if(e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}return{next:N}}function N(){return{value:t,done:!0}}return y.prototype=L.constructor=v,v.constructor=y,y.displayName=c(v,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,c(e,s,"GeneratorFunction")),e.prototype=Object.create(L),e},e.awrap=function(e){return{__await:e}},x(B.prototype),B.prototype[i]=function(){return this},e.AsyncIterator=B,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new B(u(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},x(L),c(L,s,"Generator"),L[a]=function(){return this},L.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=T,_.prototype={constructor:_,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],s=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;O(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}(e.exports);try{regeneratorRuntime=t}catch(e){Function("r","regeneratorRuntime = r")(t)}}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}};return t[e](a,a.exports,n),a.exports}n.m=t,e=[],n.O=(t,r,o,a)=>{if(!r){var i=1/0;for(u=0;u<e.length;u++){for(var[r,o,a]=e[u],s=!0,c=0;c<r.length;c++)(!1&a||i>=a)&&Object.keys(n.O).every((e=>n.O[e](r[c])))?r.splice(c--,1):(s=!1,a<i&&(i=a));s&&(e.splice(u--,1),t=o())}return t}a=a||0;for(var u=e.length;u>0&&e[u-1][2]>a;u--)e[u]=e[u-1];e[u]=[r,o,a]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={773:0,432:0,170:0};n.O.j=t=>0===e[t];var t=(t,r)=>{var o,a,[i,s,c]=r,u=0;for(o in s)n.o(s,o)&&(n.m[o]=s[o]);for(c&&c(n),t&&t(r);u<i.length;u++)a=i[u],n.o(e,a)&&e[a]&&e[a][0](),e[i[u]]=0;n.O()},r=self.webpackChunk=self.webpackChunk||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.O(void 0,[432,170],(()=>n(80))),n.O(void 0,[432,170],(()=>n(662)));var o=n.O(void 0,[432,170],(()=>n(328)));o=n.O(o)})();</script>

    <script  src="https://bio.link/js/snowfall.js"></script>
</html>