!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("react"),require("create-react-class"),require("@pake/react-color")):"function"==typeof define&&define.amd?define(["react","create-react-class","@pake/react-color"],t):e.NetlifyCMSWidgetColor=t(e.<PERSON><PERSON>,e.createClass,e.ReactColor)}(this,function(e,t,o){"use strict";function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e}).apply(this,arguments)}e=e&&e.hasOwnProperty("default")?e.default:e,t=t&&t.hasOwnProperty("default")?t.default:t;return{Control:t({displayName:"Control",getDefaultProps:function(){return{value:""}},getInitialState:function(){return{displayColorPicker:!1}},handleChangeComplete:function(e){var t=this.props,o=t.field,r=t.onChange,a=!o.get("alpha",!0),l=o.get("format")||"hex",i=e[l];if("string"!=typeof i){var n="".concat(l).concat(a?"a":""),s=Object.values(i).join(", ");i="".concat(n,"(").concat(s,")")}r(i)},handleClick:function(){var e=this.state.displayColorPicker;this.setState({displayColorPicker:!e})},handleClose:function(){this.setState({displayColorPicker:!1})},render:function(){var e=this.props,t=e.forID,a=e.field,l=e.value,i=e.classNameWrapper,n=e.setActiveStyle,s=e.setInactiveStyle,c={presetColors:void 0,color:l||a.get("default")||"#ffffff",disableAlpha:!a.get("alpha",!0)};a.has("presets")&&(c.presetColors=a.get("presets").toArray());var p=this.state.displayColorPicker,d={color:{width:"30px",height:"30px",borderRadius:"50%",background:c.color,float:"left",marginRight:"10px"},swatch:{minWidth:"120px",padding:"8px",background:"#ffffff",display:"inline-block",cursor:"pointer",borderRadius:"25px",textAlign:"left"},hex:{verticalAlign:"middle",lineHeight:"30px"},popover:{position:"absolute",zIndex:"2"},cover:{position:"fixed",top:"0px",right:"0px",bottom:"0px",left:"0px"}};return h("div",{id:t,className:i,onFocus:n,onBlur:s},h("button",{style:d.swatch,onClick:this.handleClick,type:"button"},h("div",{style:d.color}),h("span",{style:d.hex},c.color)),p?h("div",{style:d.popover},h("div",{tabIndex:0,role:"button",style:d.cover,onClick:this.handleClose,onKeyPress:this.handleClose}),h(o.SketchPicker,r({onChangeComplete:this.handleChangeComplete},c))):null)}})}});
//# sourceMappingURL=color.min.js.map
