# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [3.1.2] - 2022-09-27

### Changed

- Moved documentation from wiki to repo
- Bumped up NPM dependencies
- Created this `CHANGELOG.md` file
- Cleaned up the `README.md` file

## [3.1.1] - 2021-04-15

### Changed

- Added `formspree_form_id` config
- Bumped up dependencies

### Fixed

- Fixed minor bugs

## [3.1.0] - 2020-04-02

### Added

- Added MathJax library to render math expressions, thanks to [@XieGuochao](https://github.com/XieGuochao)

### Changed

- Updated Google Analytics script, thanks to [@JHLeeeMe](https://github.com/JHLeeeMe)

### Fixed

- Fixed hero URL, thanks to [@<PERSON><PERSON><PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON><PERSON><PERSON><PERSON>)

## [3.0.2] - 2020-02-05

### Fixed

- Added assets folder

## [3.0.1] - 2020-02-05

### Fixed

- Fixed post SVG icons

## [3.0.0] - 2020-02-05

### Added

- Created theme `gem`
- Enabled text translations
- Added heading anchor links

### Changed

- Changed code highlight colors
- Changed from Stylus to SASS

## [2.0.1] - 2020-01-30

### Changed

- Optimized to support disabled JS

### Fixed

- Fixed bugs

## [2.0.0] - 2019-08-24

### Added

- Added optional [sidebar](docs/features.md#posts-sidebar)
- Added optional [Featured post](docs/features.md#featured-post)
- Added optional ["Before you go" modal](docs/features.md#before-you-go-modal)
- Added optional [post pagination](docs/features.md#paginated-posts)
- Added [post recommendation](docs/features.md#post-recommendation)
- Added meta keywords to improve SEO
- Added JSON-LD to improve SEO
- Set up [Netlify CMS](docs/features.md#netlify-cms-ready)

### Changed

- Changed pagination to be [optional](docs/features.md#home-page-pagination)
- Improved [Tags page](docs/features.md#tags-page)
- Cleaned up and improved [Front Matter properties](docs/post.md#front-matter-properties)
- Improved customization settings
- Minor design updates

## [1.0.1] - 2019-08-16

### Changed

- Upgraded to Gulp 4

### Fixed

- Fixed bugs

## [1.0.0] - 2019-08-16

- Initial release

