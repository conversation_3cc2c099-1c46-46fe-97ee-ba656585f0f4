---
date: 2022-10-19T23:48:05.000Z
layout: post
title: Welcome to Cyber Ducky World!
subtitle: 'Introduction to my mission!'
description: >-
 An introduction to my mission for the world of cyber. I am here to help
 make cyber security entertaining! 
image: >-
 https://res.cloudinary.com/dhadtcktb/image/upload/v1666200229/CyberDucky/pexels-cottonbro-8721342_ktxs5g.jpg
optimized_image: >-
  https://res.cloudinary.com/dhadtcktb/image/upload/c_scale,w_380/v1666200229/CyberDucky/pexels-cottonbro-8721342_ktxs5g.jpg
category: blog
tags:
  - welcome
  - cyberducky
  - hacks
author: <PERSON>
paginate: true
---
I'd like to first and foremost, welcome you to the world of never ending learning. 
I created this Cyber Ducky Movement to educate the world about cyber security and technology. There
is a beauty in being able to understand very complex systems and then modify
them as you please. Everything done in this movement is for 
educational purposes only. We will explore many aspects of cyber security
through analysis, coding, and practice. 

## What exactly is the Cyber Ducky Movement?

The movement has to do with the origins of the rubber ducky. The rubber ducky has been used for many years to help break down
difficult ideas and issues that can't be solved by talking to yourself about them. The Cyber <PERSON>y is a tool and a symbol of
open mindedness. By interacting with the ducky, you can understand how issues are solved, where you went wrong in a design, or 
how you can do better. The Cyber Ducky will help you think in many different ways simply by forcing you to think about the issues. 

Ways to use the Cyber Ducky:

* Explain the whole concept of your new project
* Teach it what you just learned
* Run through your pipeline and the processes you use to 
make your code and explain how each individual part works. 
* Attach notes to your ducky to remind you of things to do
* Give it a name and hang out with it! 
* Make it fly
* Just learn with it all the time

## Mission

My mission is to help everyone from all ages learn about cyber security in an entertaining way. 
