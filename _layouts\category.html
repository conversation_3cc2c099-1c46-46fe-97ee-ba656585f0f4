<!DOCTYPE html>
<html lang="{{ site.language }}" class="no-js">
    {% include head.html %}
    <body class="main-page has-push-menu">
        {% include svg-icons.html %}
        {% include header.html %}
        <section class="content">
            <main class="home" id="post" role="main">
                <h1 class="title-category">/{{page.title}}</h1>
                <div id="grid" class="row flex-grid">
                    {% assign posts = site.posts | where_exp:"post","post.is_generated != true" %}
                    {% for post in posts %}
                        {% if post.category == page.slug %}
                            <article class="box-item">
                                <div class="box-body">
                                    {% if post.image %}
                                        <a class="cover" href="{{ post.url | prepend: site.baseurl }}">
                                            {% include loader.html %}
                                            {% if post.optimized_image %}
                                                <img src="/assets/img/placeholder.png" width="100%" data-url="{{ post.optimized_image }}" class="preload">
                                                <noscript>
                                                    <img src="{{ post.optimized_image }}" width="100%">
                                                </noscript>
                                            {% elsif post.image %}
                                                <img src="/assets/img/placeholder.png" width="100%" data-url="{{ post.image }}" class="preload">
                                                <noscript>
                                                    <img src="{{ post.image }}" width="100%">
                                                </noscript>
                                            {% else %}
                                                <img src="/assets/img/placeholder.png" width="100%" data-url="/assets/img/off.jpg" class="preload">
                                                <noscript>
                                                    <img src="/assets/img/off.jpg" width="100%">
                                                </noscript>
                                            {% endif %}
                                            {% include new-post-tag.html date=post.date %}
                                            {% include read-icon.html %}
                                        </a>
                                    {% endif %}
                                    <div class="box-info">
                                        <time datetime="{{ post.date | date_to_xmlschema }}" class="date">
                                            {% include date.html date=post.date %}
                                        </time>
                                        <a class="post-link" href="{{ post.url | prepend: site.baseurl }}">
                                            <h2 class="post-title">
                                                {{ post.title }}
                                            </h2>
                                        </a>
                                        <a class="post-link" href="{{ post.url | prepend: site.baseurl }}">
                                            <p class="description">{{ post.description }}</p>
                                        </a>
                                        <div class="tags">
                                            {% for tag in post.tags %}
                                                {% if tag != "" %}
                                                    <a href="{{ site.baseurl}}/tags/#{{tag | slugify }}">#{{ tag }}</a>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </article>
                        {% endif %}
                    {% endfor %}
                </div>
            </main>
        </section>
        {% include footer.html %}
    </body>
</html>



