# Site Settings
name: CyberDucky
title: CyberDucky | My CyberVerse
description: A location where I'll go through anything related to Cyber Security!
tags:
  - blog
  - template
  - jekyll
  - theme
  - netlify
show_hero: true
menu:
  - title: Home
    url: /
  - title: About
    url: /about
  - title: Contact
    url: /contact
  - title: Feed
    url: /feed.xml
  - title: All Links
    url: /links
email: <EMAIL>
# See: https://disqus.com/
disqus_username: cyberducky
# Contact Form Setting
# See: https://formspree.io/
formspree_form_id: xzbwaggj


# Social Media Settings
# Remove the item if you don't need it
github_username: cyberducky0o0
twitter_username: <PERSON><PERSON>_<PERSON><PERSON>_
instagram_username: cyber_ducky_
linkedin_username: juan-s-6b241b173
medium_username: cyberducky
youtube_username: cyber_ducky


# Posts Settings
show_time_bar: true
show_modal_on_exit: false
show_modal_on_finish_post: true
two_columns_layout: true


# Advanced Settings
baseurl: "" # the subpath of your site, e.g. /blog
url: "" # the base hostname & protocol for your site
google_analytics: "G-E601LYPV9Z"
language: "en"
categories_folder: category
sent_message_url: "/contact/message-sent/"


# Build settings
markdown: kramdown
highlighter: rouge
permalink: /:title/
collections:
  authors:
    output: true
paginate_path: "/page/:num/"
show_get_theme_btn: true
use_logo: false

# Content paginator
paginate_content:
  enabled: true
  debug: false
  collections:
    - posts
  auto: false
  separator: "--page-break--"
  permalink: "/:num/"
  seo_canonical: true
  properties:
    part:
      is_generated: true
    last:
      is_generated: true
    single:
      is_generated: true

# SASS
sass:
  style: compressed

# Plugins
plugins:
  - jekyll-paginate
  - jekyll-paginate-content

# Exclude my node related stuff
exclude: [
  'docs/',
  'vendor/',
  'LICENSE',
  'CHANGELOG.md',
  'jekflix.gemspec',
  'package.json',
  'package-lock.json',
  'src',
  'node_modules',
  'initpost.sh',
  'Gemfile',
  'Gemfile.lock',
  'gulpfile.js',
  'README.md'
]

# Theme
version: Quack
