<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    {% assign title = '' %}
    {% if page.layout == 'author' %}
        {% assign title = page.display_name %}
    {% elsif page.title %}
        {% assign title = page.title | append: " | " | append: site.name %}
    {% else %}
        {% assign title = site.title %}
    {% endif %}

    {% assign image = '' %}
    {% if page.image %}
        {% assign image = page.image %}
    {% else %}
        {% assign image = "/assets/img/blog-image.png" | prepend: site.baseurl | prepend: site.url %}
    {% endif %}

    {% assign description = '' %}
    {% if page.description %}
        {% assign description = page.description | strip_html | strip_newlines | truncate: 160 %}
    {% else %}
        {% assign description = site.description %}
    {% endif %}

    {% assign url = '' %}
    {% if paginator.page_trail %}
        {% assign url = paginator.first_page_path | replace:'index.html','' | prepend: site.baseurl | prepend: site.url %}
    {% else %}
        {% assign url = page.url | replace:'index.html','' | prepend: site.baseurl | prepend: site.url %}
    {% endif %}

    <title>{{ title }}</title>
    <meta name="description" content="{{ description }}">
    {% if page.tags %}
        <meta name="keywords" content="{{ page.tags | join: ', ' }}">
    {% endif %}

    <!-- Social: Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ title }}">
    <meta name="twitter:description" content="{{ description }}">

    {% if image contains '://' %}
        <meta property="twitter:image" content="{{ image }}">
    {% else %}
        <meta property="twitter:image" content="{{ image | prepend: site.url }}">
    {% endif %}
    
    {% if site.twitter_username %}
        <meta name="twitter:site" content="@{{ site.twitter_username }}">
    {% endif %}

    <!-- Social: Facebook / Open Graph -->
    <meta property="og:url" content="{{ url }}">
    <meta property="og:title" content="{{ title }}">
    <meta property="og:image" content="{{ image }}">
    <meta property="og:description" content="{{ description }}">
    <meta property="og:site_name" content="{{ site.title }}">

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ site.baseurl }}/favicon.ico" type="image/x-icon" />
    
    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="{{ site.baseurl }}/assets/img/icons/apple-touch-icon.png" />
    <link rel="apple-touch-icon" sizes="57x57" href="/assets/img/icons/apple-touch-icon-57x57.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="/assets/img/icons/apple-touch-icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="114x114" href="/assets/img/icons/apple-touch-icon-114x114.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/assets/img/icons/apple-touch-icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="60x60" href="/assets/img/icons/apple-touch-icon-60x60.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="/assets/img/icons/apple-touch-icon-120x120.png" />
    <link rel="apple-touch-icon" sizes="76x76" href="/assets/img/icons/apple-touch-icon-76x76.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/assets/img/icons/apple-touch-icon-152x152.png" />

    <!-- Windows 8 Tile Icons -->
    <meta name="application-name" content="{{ site.name }}">
    <meta name="msapplication-TileColor" content="#141414">
    <meta name="msapplication-square70x70logo" content="smalltile.png" />
    <meta name="msapplication-square150x150logo" content="mediumtile.png" />
    <meta name="msapplication-wide310x150logo" content="widetile.png" />
    <meta name="msapplication-square310x310logo" content="largetile.png" />
    
    <!-- Android Lolipop Theme Color -->
    <meta name="theme-color" content="#141414">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Titillium+Web:300,400,700" rel="stylesheet">

    <link rel="stylesheet" href="{{ '/assets/css/styles.css' | prepend: site.baseurl }}">
    <link rel="canonical" href="{{ url }}">
    <link rel="alternate" type="application/rss+xml" title="{{ site.title }}" href="{{ '/feed.xml' | prepend: site.baseurl | prepend: site.url }}" />

    <!-- Include extra styles -->
    {% include extra-css.html %}

    <!-- JavaScript enabled/disabled -->
    <script>
        document.querySelector('html').classList.remove('no-js');
    </script>
</head>
