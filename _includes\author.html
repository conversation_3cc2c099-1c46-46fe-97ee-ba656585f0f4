{% assign author = site.authors | where: "name", page.author | first %}
{% if author %}
  <section class="author">
    <div class="details">
      {% if author.photo %}
        <img class="img-rounded" src="{{ author.photo }}" alt="{{ author.display_name }}">
      {% else %}
        <img class="img-rounded" src="/assets/img/user.jpg" alt="{{ author.display_name }}">
      {% endif %}
      <p class="def">{{ site.translations.text.author | default: "Author" }}</p>
      <h3 class="name">
        <a href="{{ author.url }}">{{ author.display_name }}</a>
      </h3>
      <p class="desc">{{ author.bio }}</p>
      <p>
        {% if author.github_username %}
          <a href="https://github.com/{{ author.github_username }}" title="Github">
            <svg><use xlink:href="#icon-github"></use></svg>
          </a>
        {% endif %}
        {% if author.facebook_username %}
          <a href="https://www.facebook.com/{{ author.facebook_username }}" title="Facebook">
            <svg><use xlink:href="#icon-facebook"></use></svg>
          </a>
        {% endif %}
        {% if author.twitter_username %}
          <a href="https://twitter.com/{{ author.twitter_username }}" title="Twitter">
            <svg><use xlink:href="#icon-twitter"></use></svg>
          </a>
        {% endif %}
        {% if author.medium_username %}
          <a href="https://medium.com/@{{ author.medium_username }}" title="Medium">
            <svg><use xlink:href="#icon-medium"></use></svg>
          </a>
        {% endif %}
        {% if author.instagram_username %}
          <a href="https://www.instagram.com/{{ author.instagram_username }}" title="Instagram">
            <svg><use xlink:href="#icon-instagram"></use></svg>
          </a>
        {% endif %}
        {% if author.linkedin_username %}
          <a href="https://www.linkedin.com/in/{{ author.linkedin_username }}" title="LinkedIn">
            <svg><use xlink:href="#icon-linkedin"></use></svg>
          </a>
        {% endif %}
      </p>
    </div>
  </section>

  {% assign author_urls = '' %}
  {% if author.github_username %}
      {% assign author_urls = author_urls | append: '"https://github.com/' | append: author.github_username | append: '",' %}
  {% endif %}
  {% if author.facebook_username %}
      {% assign author_urls = author_urls | append: '"https://www.facebook.com/' | append: author.facebook_username | append: '",' %}
  {% endif %}
  {% if author.twitter_username %}
      {% assign author_urls = author_urls | append: '"https://twitter.com/' | append: author.twitter_username | append: '",' %}
  {% endif %}
  {% if author.medium_username %}
      {% assign author_urls = author_urls | append: '"https://medium.com/@' | append: author.medium_username | append: '",' %}
  {% endif %}
  {% if author.instagram_username %}
      {% assign author_urls = author_urls | append: '"https://www.instagram.com/' | append: author.instagram_username | append: '",' %}
  {% endif %}
  {% if author.linkedin_username %}
      {% assign author_urls = author_urls | append: '"https://www.linkedin.com/in/' | append: author.linkedin_username | append: '",' %}
  {% endif %}

  <script type="application/ld+json">
  {
      "@context": "http://schema.org",
      "@type": "Person",
      "name": "{{ author.display_name }}",
      {% if author.photo %}
      "image": "{{ author.photo }}",
      {% else %}
      "image": {{ "/assets/img/user.jpg" | prepend: site.baseurl | prepend: site.url }},
      {% endif %}
      "jobTitle": "{{ author.position }}",
      "url": "{{ author.url | prepend: site.baseurl | prepend: site.url }}",
      "sameAs": [
        {{ author_urls | split: "," | join: "," }}
      ]
  }
  </script>
{% endif %}
